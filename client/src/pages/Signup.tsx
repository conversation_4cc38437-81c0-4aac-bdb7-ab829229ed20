import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { MailCheck } from "lucide-react";
import { useSignupMutation } from "../service/operations/authAPI";
import Input from "../components/common/Input";
import Password from "../components/common/Password";
import Button from "../components/common/Button";

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const Signup: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<SignupFormData>({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });
  const navigate = useNavigate();
  const { mutate: signup, isPending } = useSignupMutation();
  const [successMessage, setSuccessMessage] = useState(false);
  const password = watch("password");

  const onSubmit = (data: SignupFormData) => {
    signup(data, {
      onSuccess: () => {
        setSuccessMessage(true);
        reset();
        setTimeout(() => {
          navigate("/auth/login");
        }, 5000);
      },
    });
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => clearTimeout(undefined);
  }, []);

  if (successMessage) {
    return (
      <div className="flex flex-col items-center justify-center">
        <MailCheck
          size={48}
          className="mx-auto mb-4 text-primary-500 dark:text-primary-400"
        />
        <h1 className="text-2xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
          Email Verification Sent
        </h1>
        <p className="text-neutral-600 dark:text-neutral-400 mb-6 text-center">
          We have sent you an email verification. Please verify yourself.
        </p>
        <Button
          variant="primary"
          onClick={() => navigate("/auth/login")}
          className="text-sm"
        >
          Go to Login
        </Button>
      </div>
    );
  }

  return (
    <>
      <h1 className="text-2xl font-semibold text-neutral-900 dark:text-neutral-100 mb-8">
        Create Account
      </h1>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full flex flex-col gap-5"
      >
        <div className="grid grid-cols-2 gap-4">
          <Input
            label="First Name"
            star
            placeholder="John"
            errors={errors.firstName}
            {...register("firstName", {
              required: "First name is required",
            })}
          />
          <Input
            label="Last Name"
            star
            placeholder="Doe"
            errors={errors.lastName}
            {...register("lastName", {
              required: "Last name is required",
            })}
          />
        </div>
        <Input
          label="Email"
          star
          placeholder="<EMAIL>"
          type="email"
          errors={errors.email}
          {...register("email", {
            required: "Email is required",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
              message: "Invalid email address",
            },
          })}
        />
        <Password
          label="Password"
          star
          placeholder="Enter your password"
          errors={errors.password}
          {...register("password", {
            required: "Password is required",
            minLength: {
              value: 8,
              message: "Password must be at least 8 characters",
            },
          })}
        />
        <Password
          label="Confirm Password"
          star
          placeholder="Confirm your password"
          errors={errors.confirmPassword}
          {...register("confirmPassword", {
            required: "Please confirm your password",
            validate: (value) =>
              value === password || "Passwords do not match",
          })}
        />
        <Button
          type="submit"
          loading={isPending}
          className="w-full"
          variant="primary"
        >
          Sign Up
        </Button>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-3">
          Already have an account?{" "}
          <Link
            to="/auth/login"
            className="text-primary-500 dark:text-primary-400 hover:underline"
          >
            Login
          </Link>
          .
        </p>
      </form>
    </>
  );
};

export default Signup;