import React, { useState, useEffect, useRef } from "react"
import {
  ArrowRight,
  BarChart3,
  Brain,
  Database,
  Sun,
  Shield,
  Cpu,
  Satellite,
  Waves,
  Leaf,
  Building,
  Factory,
  Home as HomeIcon,
  MapPin,
  Battery,
  Grid3X3,
  Lightbulb,
  Droplets,
  Menu,
  X,
  ChevronDown,
  Globe,
  Target,
  Eye,
  Rocket,
  Layers,
  Activity,
  CheckCircle,
  Users,
  Award,
  TrendingUp,
  Star,
  Quote,
  Mail,
  Phone,
  LocateIcon as Location,
  Calendar,
  Download,
  Share2,
  Send,
} from "lucide-react"

// Custom hook for intersection observer
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [options])

  return [ref, isIntersecting] as const
}

// Animated Counter Component
interface AnimatedCounterProps {
  end: number
  duration?: number
  isVisible: boolean
  suffix?: string
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({ end, duration = 2000, isVisible, suffix = "" }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    let startTime: number | null = null
    const animate = (currentTime: number) => {
      if (startTime === null) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration, isVisible])

  return (
    <span>
      {count}
      {suffix}
    </span>
  )
}

// Auto-scrolling Image Carousel
const ImageCarousel: React.FC = () => {
  const images = [
    {
      url: "https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Advanced Solar Forecasting",
      subtitle: "AI-Powered Predictions",
    },
    {
      url: "https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Real-time Analytics",
      subtitle: "Live Performance Monitoring",
    },
    {
      url: "https://images.unsplash.com/photo-1497440001374-f26997328c1b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Smart Energy Solutions",
      subtitle: "Optimized Generation"
    },
    {
      url: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Predictive Intelligence",
      subtitle: "Future-Ready Technology",
    },
    {
      url: "https://images.unsplash.com/photo-1508514177221-188b1cf16e9d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Renewable Energy Future",
      subtitle: "Sustainable Tomorrow"
    },
  ]

  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [images.length])

  return (
    <div className="relative h-full w-full overflow-hidden rounded-2xl shadow-2xl">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
            index === currentIndex ? "opacity-100 scale-100" : "opacity-0 scale-110"
          }`}
        >
          <div className="h-full w-full bg-cover bg-center relative" style={{ backgroundImage: `url(${image.url})` }}>
            <div className="absolute inset-0 bg-gradient-to-t from-primary-900/80 via-primary-600/40 to-transparent" />
            <div className="absolute bottom-8 left-8 right-8">
              <h3 className="text-4xl font-bold text-white mb-2 font-poppins tracking-wide">{image.text}</h3>
              <p className="text-xl text-primary-100 font-inter">{image.subtitle}</p>
              <div className="w-24 h-1 bg-gradient-to-r from-primary-400 to-primary-300 rounded-full mt-4" />
            </div>
          </div>
        </div>
      ))}

      {/* Indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex ? "bg-primary-400 scale-125" : "bg-white/50"
            }`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}

// Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: "primary" | "secondary" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  className?: string
}

const Button: React.FC<ButtonProps> = ({ children, variant = "primary", size = "md", className = "", onClick, ...props }) => {
  const baseClasses =
    "inline-flex items-center justify-center font-semibold transition-all duration-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2"

  const variants = {
    primary:
      "bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl focus:ring-primary-500",
    secondary: "bg-white border-2 border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",
    outline: "border-2 border-gray-300 text-gray-700 hover:border-primary-600 hover:text-primary-600 focus:ring-primary-500",
    ghost: "text-gray-600 hover:text-primary-600 hover:bg-primary-50 focus:ring-primary-500",
  }

  const sizes = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
  }

  return (
    <button className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`} onClick={onClick} {...props}>
      {children}
    </button>
  )
}

// Card Component
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

const Card: React.FC<CardProps> = ({ children, className = "", ...props }) => {
  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-100 ${className}`} {...props}>
      {children}
    </div>
  )
}

// Badge Component
interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode
  className?: string
}

const Badge: React.FC<BadgeProps> = ({ children, className = "", ...props }) => {
  return (
    <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${className}`} {...props}>
      {children}
    </span>
  )
}

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

const Input: React.FC<InputProps> = ({ className = "", ...props }) => {
  return (
    <input
      className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 ${className}`}
      {...props}
    />
  )
}

const Home: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isLoginOpen, setIsLoginOpen] = useState(false)

  // Intersection observer refs
  const [heroRef, heroVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [servicesRef, servicesVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [statsRef, statsVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [featuresRef, featuresVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [testimonialsRef, testimonialsVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [pricingRef, pricingVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [aboutRef, aboutVisible] = useIntersectionObserver({ threshold: 0.1 })

  const solarSystems = [
    {
      icon: HomeIcon,
      title: "Residential Rooftop",
      description: "Smart home solar solutions with AI-powered optimization for maximum efficiency",
      features: ["Smart monitoring", "Cost optimization", "Performance analytics"],
      gradient: "from-primary-500 to-secondary-500",
    },
    {
      icon: Building,
      title: "Commercial Rooftop",
      description: "Enterprise-grade solar installations for businesses and commercial buildings",
      features: ["Enterprise monitoring", "ROI tracking", "Energy management"],
      gradient: "from-secondary-500 to-accent-500",
    },
    {
      icon: Factory,
      title: "Ground Mount Farms",
      description: "Large-scale solar farms with precision forecasting and optimization",
      features: ["Utility-scale monitoring", "Grid integration", "Performance optimization"],
      gradient: "from-success-500 to-primary-500",
    },
    {
      icon: Waves,
      title: "Floating Solar",
      description: "Innovative floatovoltaic systems on water bodies with specialized monitoring",
      features: ["Water surface monitoring", "Environmental tracking", "Efficiency optimization"],
      gradient: "from-primary-500 to-secondary-500",
    },
    {
      icon: Leaf,
      title: "Agrivoltaics",
      description: "Dual-use solar and agriculture optimization for sustainable farming",
      features: ["Crop monitoring", "Dual-use optimization", "Agricultural analytics"],
      gradient: "from-success-500 to-secondary-500",
    },
    {
      icon: Grid3X3,
      title: "BIPV Systems",
      description: "Building-integrated photovoltaic solutions seamlessly integrated into architecture",
      features: ["Architectural integration", "Aesthetic optimization", "Building analytics"],
      gradient: "from-accent-500 to-error-500",
    },
    {
      icon: Satellite,
      title: "Solar Tracking",
      description: "Dynamic tracking systems for maximum sun exposure and energy generation",
      features: ["Sun tracking", "Dynamic optimization", "Movement analytics"],
      gradient: "from-secondary-500 to-primary-500",
    },
    {
      icon: Battery,
      title: "Hybrid Systems",
      description: "Solar + storage integrated solutions for 24/7 energy availability",
      features: ["Storage optimization", "Grid balancing", "Energy arbitrage"],
      gradient: "from-accent-500 to-secondary-500",
    },
    {
      icon: MapPin,
      title: "Off-Grid Solar",
      description: "Remote area solar installations with autonomous operation capabilities",
      features: ["Remote monitoring", "Autonomous operation", "Reliability tracking"],
      gradient: "from-primary-500 to-success-500",
    },
    {
      icon: Lightbulb,
      title: "Solar Street Lighting",
      description: "Smart street lighting with solar power and intelligent control systems",
      features: ["Smart controls", "Motion sensing", "Energy efficiency"],
      gradient: "from-accent-500 to-primary-500",
    },
    {
      icon: Droplets,
      title: "Solar Water Pumping",
      description: "Agricultural water pumping solutions with weather-based optimization",
      features: ["Weather integration", "Pump optimization", "Water management"],
      gradient: "from-primary-500 to-success-500",
    },
    {
      icon: Target,
      title: "PM Kusum Scheme",
      description: "Government scheme solar installations with compliance monitoring",
      features: ["Compliance tracking", "Subsidy optimization", "Government reporting"],
      gradient: "from-accent-500 to-secondary-500",
    },
  ]

  const features = [
    {
      icon: Brain,
      title: "Advanced AI Models",
      description: "Deep learning algorithms trained on millions of data points for unprecedented accuracy",
      color: "text-primary-600",
      bgColor: "bg-primary-50",
    },
    {
      icon: Activity,
      title: "Real-time Analytics",
      description: "Live monitoring and instant performance insights with millisecond response times",
      color: "text-success-600",
      bgColor: "bg-success-50",
    },
    {
      icon: Shield,
      title: "99.5% Accuracy",
      description: "Industry-leading prediction accuracy with continuous learning and model improvement",
      color: "text-secondary-600",
      bgColor: "bg-secondary-50",
    },
    {
      icon: Rocket,
      title: "Instant Processing",
      description: "Lightning-fast analysis and forecasting results delivered in real-time",
      color: "text-accent-600",
      bgColor: "bg-accent-50",
    },
    {
      icon: Globe,
      title: "Global Weather Data",
      description: "Integrated satellite and meteorological data sources from around the world",
      color: "text-primary-600",
      bgColor: "bg-primary-50",
    },
    {
      icon: Layers,
      title: "Multi-layer Analysis",
      description: "Comprehensive analysis across multiple data dimensions and environmental factors",
      color: "text-accent-600",
      bgColor: "bg-accent-50",
    },
  ]

  const testimonials = [
    {
      name: "Dr. Rajesh Kumar",
      position: "Chief Technology Officer",
      company: "SolarTech Industries",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80",
      rating: 5,
      text: "Rajesh Power Service has revolutionized our solar forecasting capabilities. Their AI models have improved our prediction accuracy by 40% and helped us optimize our energy production significantly.",
    },
    {
      name: "Priya Sharma",
      position: "Operations Manager",
      company: "Green Energy Solutions",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80",
      rating: 5,
      text: "The platform's real-time analytics and forecasting have been game-changing for our solar farm operations. We've seen a 25% increase in efficiency since implementing their solution.",
    },
    {
      name: "Amit Patel",
      position: "Renewable Energy Consultant",
      company: "EcoVision Consulting",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80",
      rating: 5,
      text: "Outstanding service and cutting-edge technology. The AI-powered predictions have helped our clients make better investment decisions and optimize their solar installations.",
    },
  ]

  const pricingPlans = [
    {
      name: "Starter",
      price: "₹9,999",
      period: "/month",
      description: "Perfect for small residential installations",
      features: [
        "Up to 5 solar installations",
        "Basic AI forecasting",
        "Daily predictions",
        "Email support",
        "Mobile app access",
      ],
      popular: false,
    },
    {
      name: "Professional",
      price: "₹24,999",
      period: "/month",
      description: "Ideal for commercial and medium-scale projects",
      features: [
        "Up to 50 solar installations",
        "Advanced AI forecasting",
        "Hourly predictions",
        "Priority support",
        "API access",
        "Custom reports",
        "Weather integration",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "For large-scale solar farms and utilities",
      features: [
        "Unlimited installations",
        "Premium AI models",
        "Real-time predictions",
        "24/7 dedicated support",
        "Full API access",
        "Custom integrations",
        "Advanced analytics",
        "White-label solution",
      ],
      popular: false,
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary-900 via-secondary-800 to-brand-blue-900 text-white overflow-x-hidden font-inter neural-network particles">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-secondary-900/95 backdrop-blur-xl border-b border-brand-blue-500/30 shadow-2xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-brand-blue-500 to-cyber-500 rounded-2xl flex items-center justify-center shadow-xl animate-glow border border-brand-blue-400/30">
                <Sun className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white font-poppins tracking-wide">Rajesh Power Service</h1>
                <p className="text-sm text-cyber-400 font-medium">AI-Powered Energy Forecasting</p>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center space-x-6">
              <a
                href="#services"
                className="text-gray-300 hover:text-cyber-400 transition-all duration-300 font-medium text-sm px-4 py-2 rounded-lg hover:bg-cyber-500/10 border border-transparent hover:border-cyber-500/30"
              >
                AI Services
              </a>
              <a
                href="#systems"
                className="text-gray-300 hover:text-cyber-400 transition-all duration-300 font-medium text-sm px-4 py-2 rounded-lg hover:bg-cyber-500/10 border border-transparent hover:border-cyber-500/30"
              >
                Solar Systems
              </a>
              <a
                href="#features"
                className="text-gray-300 hover:text-cyber-400 transition-all duration-300 font-medium text-sm px-4 py-2 rounded-lg hover:bg-cyber-500/10 border border-transparent hover:border-cyber-500/30"
              >
                AI Features
              </a>
              <a
                href="#pricing"
                className="text-gray-300 hover:text-cyber-400 transition-all duration-300 font-medium text-sm px-4 py-2 rounded-lg hover:bg-cyber-500/10 border border-transparent hover:border-cyber-500/30"
              >
                Pricing
              </a>
              <a href="#about" className="text-gray-300 hover:text-cyber-400 transition-all duration-300 font-medium text-sm px-4 py-2 rounded-lg hover:bg-cyber-500/10 border border-transparent hover:border-cyber-500/30">
                About
              </a>
              <Button variant="outline" onClick={() => setIsLoginOpen(true)} className="border-cyber-500 text-cyber-400 hover:bg-cyber-500/20 text-sm px-4 py-2">
                Login
              </Button>
              <Button className="bg-gradient-to-r from-brand-blue-500 to-accent-500 hover:from-brand-blue-600 hover:to-accent-600 text-sm px-6 py-2 shadow-lg">
                Get Started
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <button className="lg:hidden text-gray-300 hover:text-cyber-400 transition-colors" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-secondary-900/98 backdrop-blur-xl border-t border-brand-blue-500/30 shadow-xl">
            <div className="px-4 py-6 space-y-3">
              <a href="#services" className="block text-gray-300 hover:text-cyber-400 transition-colors font-medium py-2 px-3 rounded-lg hover:bg-cyber-500/10">
                AI Services
              </a>
              <a href="#systems" className="block text-gray-300 hover:text-cyber-400 transition-colors font-medium py-2 px-3 rounded-lg hover:bg-cyber-500/10">
                Solar Systems
              </a>
              <a href="#features" className="block text-gray-300 hover:text-cyber-400 transition-colors font-medium py-2 px-3 rounded-lg hover:bg-cyber-500/10">
                AI Features
              </a>
              <a href="#pricing" className="block text-gray-300 hover:text-cyber-400 transition-colors font-medium py-2 px-3 rounded-lg hover:bg-cyber-500/10">
                Pricing
              </a>
              <a href="#about" className="block text-gray-300 hover:text-cyber-400 transition-colors font-medium py-2 px-3 rounded-lg hover:bg-cyber-500/10">
                About
              </a>
              <div className="pt-4 space-y-3">
                <Button variant="outline" className="w-full border-cyber-500 text-cyber-400 hover:bg-cyber-500/20" onClick={() => setIsLoginOpen(true)}>
                  Login
                </Button>
                <Button className="w-full bg-gradient-to-r from-brand-blue-500 to-accent-500">Get Started</Button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center pt-24 pb-16 cyber-grid"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div
              className={`space-y-8 transform transition-all duration-1000 ${
                heroVisible ? "translate-x-0 opacity-100" : "-translate-x-20 opacity-0"
              }`}
            >
              <div className="space-y-6">
                <Badge className="bg-cyber-500/20 text-cyber-400 border border-cyber-500/30 glass-morphism px-4 py-2 text-sm font-medium">
                  <Brain className="w-4 h-4 mr-2" />
                  Neural Network AI Forecasting
                </Badge>

                <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight font-poppins">
                  <span className="bg-gradient-to-r from-brand-blue-400 via-cyber-400 to-brand-blue-500 bg-clip-text text-transparent">
                    AI-Powered
                  </span>
                  <br />
                  <span className="text-white">Solar Energy</span>
                  <br />
                  <span className="bg-gradient-to-r from-accent-400 to-cyber-400 bg-clip-text text-transparent">
                    Intelligence
                  </span>
                </h1>

                <p className="text-lg lg:text-xl text-gray-300 leading-relaxed max-w-2xl">
                  Experience the future of renewable energy with our advanced neural networks. Our AI algorithms process
                  millions of data points in real-time to deliver unprecedented accuracy in solar energy forecasting.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="group bg-gradient-to-r from-brand-blue-500 to-cyber-500 hover:from-brand-blue-600 hover:to-cyber-600 shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-4 text-base font-semibold">
                  <Brain className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                  Start AI Analysis
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button size="lg" variant="outline" className="border-accent-500 text-accent-400 hover:bg-accent-500/20 hover:border-accent-400 glass-morphism px-8 py-4 text-base font-semibold">
                  <Eye className="w-5 h-5 mr-2" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div ref={statsRef} className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-12">
                <div className="text-center glass-morphism p-6 rounded-xl border border-cyber-500/30 hover:border-cyber-500/50 transition-all duration-300">
                  <div className="text-3xl lg:text-4xl font-bold text-cyber-400 font-poppins mb-2">
                    <AnimatedCounter end={99} isVisible={statsVisible} suffix="%" />
                  </div>
                  <div className="text-sm text-gray-300 font-medium">Neural Accuracy</div>
                </div>
                <div className="text-center glass-morphism p-6 rounded-xl border border-accent-500/30 hover:border-accent-500/50 transition-all duration-300">
                  <div className="text-3xl lg:text-4xl font-bold text-accent-400 font-poppins mb-2">
                    <AnimatedCounter end={1500} isVisible={statsVisible} suffix="+" />
                  </div>
                  <div className="text-sm text-gray-300 font-medium">AI Analyzed Plants</div>
                </div>
                <div className="text-center glass-morphism p-6 rounded-xl border border-brand-blue-500/30 hover:border-brand-blue-500/50 transition-all duration-300">
                  <div className="text-3xl lg:text-4xl font-bold text-brand-blue-400 font-poppins mb-2">
                    <AnimatedCounter end={24} isVisible={statsVisible} />
                    /7
                  </div>
                  <div className="text-sm text-gray-300 font-medium">Quantum Processing</div>
                </div>
              </div>
            </div>

            <div
              className={`relative transform transition-all duration-1000 delay-300 ${
                heroVisible ? "translate-x-0 opacity-100" : "translate-x-20 opacity-0"
              }`}
            >
              <div className="relative h-[600px] w-full">
                <ImageCarousel />
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="flex flex-col items-center space-y-2">
            <span className="text-xs text-gray-400 font-medium">Scroll to explore</span>
            <ChevronDown className="w-6 h-6 text-cyber-400" />
          </div>
        </div>
      </section>

      {/* Solar Systems Section */}
      <section id="systems" className="py-24 lg:py-32 bg-gradient-to-br from-secondary-800 to-brand-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 cyber-grid opacity-30"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div
            ref={servicesRef}
            className={`text-center space-y-6 mb-16 lg:mb-20 transform transition-all duration-1000 ${
              servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-cyber-500/20 text-cyber-400 border border-cyber-500/30 glass-morphism px-4 py-2 text-sm font-medium">
              <Cpu className="w-4 h-4 mr-2" />
              AI-Powered Solar Intelligence
            </Badge>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-cyber-400 to-brand-blue-400 bg-clip-text text-transparent">
                Neural Network
              </span>
              <br />
              <span className="text-white">Solar Analysis</span>
            </h2>
            <p className="text-lg lg:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Our advanced AI algorithms analyze and optimize every type of solar installation, from residential rooftops
              to massive utility-scale farms, delivering unprecedented accuracy and intelligence.
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
            {solarSystems.map((system, index) => (
              <Card
                key={index}
                className={`glass-morphism border border-cyber-500/20 hover:border-cyber-500/40 hover:shadow-xl hover:shadow-cyber-500/20 transition-all duration-500 group transform hover:-translate-y-2 ${
                  servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="p-6 space-y-4">
                  <div
                    className={`w-14 h-14 bg-gradient-to-br ${system.gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                  >
                    <system.icon className="w-7 h-7 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white font-poppins">{system.title}</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">{system.description}</p>
                  <ul className="space-y-2">
                    {system.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-xs text-gray-400">
                        <CheckCircle className="w-3 h-3 text-cyber-400 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 lg:py-32 bg-gradient-to-br from-brand-blue-900 to-secondary-900 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="matrix-bg h-full w-full"></div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div
            ref={featuresRef}
            className={`text-center space-y-6 mb-16 lg:mb-20 transform transition-all duration-1000 ${
              featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-accent-500/20 text-accent-400 border border-accent-500/30 glass-morphism px-4 py-2 text-sm font-medium">
              <Brain className="w-4 h-4 mr-2" />
              Quantum AI Technology
            </Badge>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-accent-400 to-cyber-400 bg-clip-text text-transparent">
                Quantum-Enhanced
              </span>
              <br />
              <span className="text-white">AI Intelligence</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`group transform transition-all duration-700 ${
                  featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <Card className="glass-morphism border border-accent-500/20 hover:border-accent-500/40 hover:shadow-xl hover:shadow-accent-500/20 transition-all duration-500 h-full group-hover:-translate-y-2">
                  <div className="p-6 lg:p-8 space-y-6">
                    <div
                      className={`w-14 h-14 ${feature.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                    >
                      <feature.icon className={`w-7 h-7 ${feature.color}`} />
                    </div>
                    <h3 className="text-xl lg:text-2xl font-bold text-white font-poppins">{feature.title}</h3>
                    <p className="text-gray-300 leading-relaxed text-sm lg:text-base">{feature.description}</p>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 lg:py-32 bg-gradient-to-br from-secondary-900 via-brand-blue-900 to-secondary-800 relative overflow-hidden">
        <div className="absolute inset-0 cyber-grid opacity-20"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center space-y-6 mb-16 lg:mb-20">
            <Badge className="bg-brand-blue-500/20 text-brand-blue-400 border border-brand-blue-500/30 glass-morphism px-4 py-2 text-sm font-medium">
              <Brain className="w-4 h-4 mr-2" />
              Neural AI Process
            </Badge>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-brand-blue-400 to-cyber-400 bg-clip-text text-transparent">
                How Our AI
              </span>
              <br />
              <span className="text-white">Neural Network Works</span>
            </h2>
            <p className="text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto">
              Our quantum-enhanced AI platform processes millions of data points to deliver unprecedented solar energy forecasting accuracy
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            {[
              {
                step: "01",
                icon: Database,
                title: "Neural Data Ingestion",
                description:
                  "Our AI instantly processes your historical generation data, weather patterns, and system specifications through quantum-enhanced data pipelines with real-time validation and neural preprocessing.",
                color: "from-brand-blue-500 to-cyber-500",
              },
              {
                step: "02",
                icon: Brain,
                title: "Quantum AI Analysis",
                description:
                  "Advanced neural networks with deep learning algorithms analyze complex patterns, environmental correlations, and predictive factors using our proprietary quantum-enhanced processing cores.",
                color: "from-cyber-500 to-accent-500",
              },
              {
                step: "03",
                icon: BarChart3,
                title: "Precision Forecasting",
                description:
                  "Receive hyper-accurate predictions with confidence intervals, risk assessments, and AI-optimized recommendations for maximum energy efficiency and intelligent ROI optimization.",
                color: "from-accent-500 to-brand-blue-500",
              },
            ].map((step, index) => (
              <div key={index} className="text-center space-y-6 group">
                <div className="relative">
                  <div
                    className={`w-20 h-20 lg:w-24 lg:h-24 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-all duration-300 shadow-2xl border border-white/20`}
                  >
                    <step.icon className="w-10 h-10 lg:w-12 lg:h-12 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-cyber-400 to-brand-blue-400 rounded-full flex items-center justify-center shadow-xl border border-white/30">
                    <span className="text-sm font-bold text-white">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-xl lg:text-2xl font-bold text-white font-poppins">{step.title}</h3>
                <p className="text-gray-300 leading-relaxed text-sm lg:text-base">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section ref={testimonialsRef} className="py-24 lg:py-32 bg-gradient-to-br from-brand-blue-800 to-secondary-900 relative overflow-hidden">
        <div className="absolute inset-0 matrix-bg opacity-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div
            className={`text-center space-y-6 mb-16 lg:mb-20 transform transition-all duration-1000 ${
              testimonialsVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-accent-500/20 text-accent-400 border border-accent-500/30 glass-morphism px-4 py-2 text-sm font-medium">
              <Users className="w-4 h-4 mr-2" />
              Neural Network Success Stories
            </Badge>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-accent-400 to-cyber-400 bg-clip-text text-transparent">
                What Our AI Partners
              </span>
              <br />
              <span className="text-white">Say About Our Intelligence</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-6 lg:gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className={`glass-morphism border border-accent-500/20 hover:border-accent-500/40 hover:shadow-xl hover:shadow-accent-500/20 transition-all duration-500 transform hover:-translate-y-2 ${
                  testimonialsVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <div className="p-6 lg:p-8 space-y-6">
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-accent-400 fill-current" />
                    ))}
                  </div>
                  <Quote className="w-8 h-8 text-cyber-400" />
                  <p className="text-gray-300 leading-relaxed italic text-sm lg:text-base">"{testimonial.text}"</p>
                  <div className="flex items-center space-x-4">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-cyber-500/30"
                    />
                    <div>
                      <h4 className="font-bold text-white text-sm lg:text-base">{testimonial.name}</h4>
                      <p className="text-sm text-gray-400">{testimonial.position}</p>
                      <p className="text-sm text-cyber-400">{testimonial.company}</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" ref={pricingRef} className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`text-center space-y-6 mb-20 transform transition-all duration-1000 ${
              pricingVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-success-100 text-success-700 border border-success-200">
              <TrendingUp className="w-4 h-4 mr-2" />
              Pricing Plans
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-success-600 to-primary-600 bg-clip-text text-transparent">
                Choose Your
              </span>
              <br />
              <span className="text-gray-900">Perfect Plan</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible pricing options designed to scale with your solar energy operations
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`relative hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${
                  plan.popular ? "ring-2 ring-primary-600 scale-105" : ""
                } ${pricingVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"}`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary-600 text-white px-4 py-2">
                      <Award className="w-4 h-4 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <div className="p-8 space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900 font-poppins">{plan.name}</h3>
                    <p className="text-gray-600 mt-2">{plan.description}</p>
                    <div className="mt-6">
                      <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                      <span className="text-gray-600">{plan.period}</span>
                    </div>
                  </div>
                  <ul className="space-y-4">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-success-500 mr-3" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${plan.popular ? "bg-gradient-to-r from-primary-600 to-primary-700" : ""}`}
                    variant={plan.popular ? "primary" : "outline"}
                  >
                    {plan.name === "Enterprise" ? "Contact Sales" : "Get Started"}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" ref={aboutRef} className="py-32 bg-gradient-to-br from-gray-900 to-primary-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`text-center space-y-8 transform transition-all duration-1000 ${
              aboutVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-white/20 text-white border border-white/30">
              <Building className="w-4 h-4 mr-2" />
              About Us
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="text-white">Rajesh Power Service</span>
              <br />
              <span className="bg-gradient-to-r from-secondary-400 to-primary-400 bg-clip-text text-transparent">Limited</span>
            </h2>
            <p className="text-xl text-primary-100 max-w-4xl mx-auto leading-relaxed">
              We are pioneers in renewable energy forecasting, combining decades of industry expertise with cutting-edge
              artificial intelligence to help solar energy producers optimize their operations and maximize efficiency.
              Our mission is to accelerate the global transition to sustainable energy.
            </p>

            <div className="grid md:grid-cols-4 gap-8 pt-12">
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-secondary-400 font-poppins">
                  <AnimatedCounter end={15} isVisible={aboutVisible} suffix="+" />
                </div>
                <div className="text-primary-100">Years Experience</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-secondary-400 font-poppins">
                  <AnimatedCounter end={2000} isVisible={aboutVisible} suffix="+" />
                </div>
                <div className="text-primary-100">MW Forecasted</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-secondary-400 font-poppins">
                  <AnimatedCounter end={100} isVisible={aboutVisible} suffix="+" />
                </div>
                <div className="text-primary-100">Enterprise Clients</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-secondary-400 font-poppins">
                  <AnimatedCounter end={50} isVisible={aboutVisible} suffix="+" />
                </div>
                <div className="text-primary-100">Countries Served</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16">
            <div className="space-y-8">
              <div>
                <Badge className="bg-primary-100 text-primary-700 border border-primary-200">
                  <Mail className="w-4 h-4 mr-2" />
                  Get In Touch
                </Badge>
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mt-4 font-poppins">
                  Ready to Transform Your
                  <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                    {" "}
                    Solar Operations?
                  </span>
                </h2>
                <p className="text-xl text-gray-600 mt-6">
                  Contact our team of experts to learn how our AI-powered forecasting can optimize your solar energy
                  generation.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Phone</h4>
                    <p className="text-gray-600">+91 98765 43210</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-success-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Email</h4>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center">
                    <Location className="w-6 h-6 text-secondary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Address</h4>
                    <p className="text-gray-600">Mumbai, Maharashtra, India</p>
                  </div>
                </div>
              </div>
            </div>

            <Card className="shadow-xl">
              <div className="p-8 space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 font-poppins">Send us a message</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <Input placeholder="Enter your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <Input placeholder="Enter your last name" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                  <Input placeholder="Enter your company name" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 h-32 resize-none"
                    placeholder="Tell us about your solar energy forecasting needs..."
                  ></textarea>
                </div>
                <Button className="w-full" size="lg">
                  <Send className="w-5 h-5 mr-2" />
                  Send Message
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-r from-primary-600 to-secondary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-6xl font-bold text-white font-poppins">
              Ready to Optimize Your
              <br />
              Solar Energy Production?
            </h2>
            <p className="text-xl text-primary-100">
              Join hundreds of solar energy producers who trust our AI-powered forecasting platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100 shadow-xl">
                <Rocket className="w-5 h-5 mr-2" />
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2 space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-secondary-500 rounded-xl flex items-center justify-center">
                  <Sun className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold font-poppins">Rajesh Power Service Limited</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Leading the future of renewable energy with AI-powered forecasting solutions. Transforming solar energy
                operations worldwide.
              </p>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Share2 className="w-5 h-5" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Download className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {[
              {
                title: "Solutions",
                links: ["Solar Forecasting", "Energy Analytics", "Performance Monitoring", "Consulting Services"],
              },
              {
                title: "Company",
                links: ["About Us", "Careers", "News", "Contact"],
              },
              {
                title: "Support",
                links: ["Documentation", "Help Center", "API Reference", "Status"],
              },
            ].map((section, index) => (
              <div key={index} className="space-y-4">
                <h3 className="text-lg font-semibold text-white font-poppins">{section.title}</h3>
                <ul className="space-y-2">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a href="#" className="text-gray-400 hover:text-white transition-colors">
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2024 Rajesh Power Service Limited. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      {isLoginOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <div className="p-8 space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-gray-900 font-poppins">Login</h2>
                <button onClick={() => setIsLoginOpen(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="w-6 h-6" />
                </button>
              </div>
              <p className="text-gray-600">Access your solar forecasting dashboard</p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <Input type="password" placeholder="Enter your password" />
                </div>
                <Button className="w-full">Sign In</Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default Home