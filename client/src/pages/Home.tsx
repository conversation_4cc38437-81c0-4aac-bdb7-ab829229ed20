import React, { useState, useEffect, useRef } from "react"
import {
  ArrowRight,
  BarChart3,
  Brain,
  Database,
  Sun,
  Shield,
  Cpu,
  Satellite,
  Waves,
  Leaf,
  Building,
  Factory,
  Home as HomeIcon,
  MapPin,
  Battery,
  Grid3X3,
  Lightbulb,
  Droplets,
  Menu,
  X,
  ChevronDown,
  Play,
  Globe,
  Target,
  Eye,
  Sparkles,
  Rocket,
  Layers,
  Settings,
  Activity,
  CheckCircle,
} from "lucide-react"

// Custom hook for intersection observer
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [options])

  return [ref, isIntersecting] as const
}

// Animated Counter Component
interface AnimatedCounterProps {
  end: number
  duration?: number
  isVisible: boolean
  suffix?: string
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({ end, duration = 2000, isVisible, suffix = "" }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    let startTime: number | null = null
    const animate = (currentTime: number) => {
      if (startTime === null) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration, isVisible])

  return (
    <span>
      {count}
      {suffix}
    </span>
  )
}

// Auto-scrolling Image Carousel
const ImageCarousel: React.FC = () => {
  const images = [
    {
      url: "https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Advanced Solar Forecasting",
      subtitle: "AI-Powered Predictions",
    },
    {
      url: "https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Real-time Analytics",
      subtitle: "Live Performance Monitoring",
    },
    {
      url: "https://images.unsplash.com/photo-1497440001374-f26997328c1b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Smart Energy Solutions",
      subtitle: "Optimized Generation"
    },
    {
      url: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Predictive Intelligence",
      subtitle: "Future-Ready Technology",
    },
    {
      url: "https://images.unsplash.com/photo-1508514177221-188b1cf16e9d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      text: "Renewable Energy Future",
      subtitle: "Sustainable Tomorrow"
    },
  ]

  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [images.length])

  return (
    <div className="relative h-full w-full overflow-hidden rounded-2xl shadow-2xl">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
            index === currentIndex ? "opacity-100 scale-100" : "opacity-0 scale-110"
          }`}
        >
          <div className="h-full w-full bg-cover bg-center relative" style={{ backgroundImage: `url(${image.url})` }}>
            <div className="absolute inset-0 bg-gradient-to-t from-primary-900/80 via-primary-600/40 to-transparent" />
            <div className="absolute bottom-8 left-8 right-8">
              <h3 className="text-4xl font-bold text-white mb-2 font-poppins tracking-wide">{image.text}</h3>
              <p className="text-xl text-primary-100 font-inter">{image.subtitle}</p>
              <div className="w-24 h-1 bg-gradient-to-r from-primary-400 to-primary-300 rounded-full mt-4" />
            </div>
          </div>
        </div>
      ))}

      {/* Indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex ? "bg-primary-400 scale-125" : "bg-white/50"
            }`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}

// Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: "primary" | "secondary" | "outline" | "ghost"
  size?: "sm" | "md" | "lg"
  className?: string
}

const Button: React.FC<ButtonProps> = ({ children, variant = "primary", size = "md", className = "", onClick, ...props }) => {
  const baseClasses =
    "inline-flex items-center justify-center font-semibold transition-all duration-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2"

  const variants = {
    primary:
      "bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl focus:ring-primary-500",
    secondary: "bg-white border-2 border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",
    outline: "border-2 border-gray-300 text-gray-700 hover:border-primary-600 hover:text-primary-600 focus:ring-primary-500",
    ghost: "text-gray-600 hover:text-primary-600 hover:bg-primary-50 focus:ring-primary-500",
  }

  const sizes = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
  }

  return (
    <button className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`} onClick={onClick} {...props}>
      {children}
    </button>
  )
}

// Card Component
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

const Card: React.FC<CardProps> = ({ children, className = "", ...props }) => {
  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-100 ${className}`} {...props}>
      {children}
    </div>
  )
}

// Badge Component
interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode
  className?: string
}

const Badge: React.FC<BadgeProps> = ({ children, className = "", ...props }) => {
  return (
    <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${className}`} {...props}>
      {children}
    </span>
  )
}

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

const Input: React.FC<InputProps> = ({ className = "", ...props }) => {
  return (
    <input
      className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 ${className}`}
      {...props}
    />
  )
}

const Home: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isLoginOpen, setIsLoginOpen] = useState(false)

  // Intersection observer refs
  const [heroRef, heroVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [servicesRef, servicesVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [statsRef, statsVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [featuresRef, featuresVisible] = useIntersectionObserver({ threshold: 0.1 })

  const solarSystems = [
    {
      icon: HomeIcon,
      title: "Residential Rooftop",
      description: "Smart home solar solutions with AI-powered optimization for maximum efficiency",
      features: ["Smart monitoring", "Cost optimization", "Performance analytics"],
      gradient: "from-primary-500 to-secondary-500",
    },
    {
      icon: Building,
      title: "Commercial Rooftop",
      description: "Enterprise-grade solar installations for businesses and commercial buildings",
      features: ["Enterprise monitoring", "ROI tracking", "Energy management"],
      gradient: "from-secondary-500 to-accent-500",
    },
    {
      icon: Factory,
      title: "Ground Mount Farms",
      description: "Large-scale solar farms with precision forecasting and optimization",
      features: ["Utility-scale monitoring", "Grid integration", "Performance optimization"],
      gradient: "from-success-500 to-primary-500",
    },
    {
      icon: Waves,
      title: "Floating Solar",
      description: "Innovative floatovoltaic systems on water bodies with specialized monitoring",
      features: ["Water surface monitoring", "Environmental tracking", "Efficiency optimization"],
      gradient: "from-primary-500 to-secondary-500",
    },
    {
      icon: Leaf,
      title: "Agrivoltaics",
      description: "Dual-use solar and agriculture optimization for sustainable farming",
      features: ["Crop monitoring", "Dual-use optimization", "Agricultural analytics"],
      gradient: "from-success-500 to-secondary-500",
    },
    {
      icon: Grid3X3,
      title: "BIPV Systems",
      description: "Building-integrated photovoltaic solutions seamlessly integrated into architecture",
      features: ["Architectural integration", "Aesthetic optimization", "Building analytics"],
      gradient: "from-accent-500 to-error-500",
    },
    {
      icon: Satellite,
      title: "Solar Tracking",
      description: "Dynamic tracking systems for maximum sun exposure and energy generation",
      features: ["Sun tracking", "Dynamic optimization", "Movement analytics"],
      gradient: "from-secondary-500 to-primary-500",
    },
    {
      icon: Battery,
      title: "Hybrid Systems",
      description: "Solar + storage integrated solutions for 24/7 energy availability",
      features: ["Storage optimization", "Grid balancing", "Energy arbitrage"],
      gradient: "from-accent-500 to-secondary-500",
    },
    {
      icon: MapPin,
      title: "Off-Grid Solar",
      description: "Remote area solar installations with autonomous operation capabilities",
      features: ["Remote monitoring", "Autonomous operation", "Reliability tracking"],
      gradient: "from-primary-500 to-success-500",
    },
    {
      icon: Lightbulb,
      title: "Solar Street Lighting",
      description: "Smart street lighting with solar power and intelligent control systems",
      features: ["Smart controls", "Motion sensing", "Energy efficiency"],
      gradient: "from-accent-500 to-primary-500",
    },
    {
      icon: Droplets,
      title: "Solar Water Pumping",
      description: "Agricultural water pumping solutions with weather-based optimization",
      features: ["Weather integration", "Pump optimization", "Water management"],
      gradient: "from-primary-500 to-success-500",
    },
    {
      icon: Target,
      title: "PM Kusum Scheme",
      description: "Government scheme solar installations with compliance monitoring",
      features: ["Compliance tracking", "Subsidy optimization", "Government reporting"],
      gradient: "from-accent-500 to-secondary-500",
    },
  ]

  const features = [
    {
      icon: Brain,
      title: "Advanced AI Models",
      description: "Deep learning algorithms trained on millions of data points for unprecedented accuracy",
      color: "text-primary-600",
      bgColor: "bg-primary-50",
    },
    {
      icon: Activity,
      title: "Real-time Analytics",
      description: "Live monitoring and instant performance insights with millisecond response times",
      color: "text-success-600",
      bgColor: "bg-success-50",
    },
    {
      icon: Shield,
      title: "99.5% Accuracy",
      description: "Industry-leading prediction accuracy with continuous learning and model improvement",
      color: "text-secondary-600",
      bgColor: "bg-secondary-50",
    },
    {
      icon: Rocket,
      title: "Instant Processing",
      description: "Lightning-fast analysis and forecasting results delivered in real-time",
      color: "text-accent-600",
      bgColor: "bg-accent-50",
    },
    {
      icon: Globe,
      title: "Global Weather Data",
      description: "Integrated satellite and meteorological data sources from around the world",
      color: "text-primary-600",
      bgColor: "bg-primary-50",
    },
    {
      icon: Layers,
      title: "Multi-layer Analysis",
      description: "Comprehensive analysis across multiple data dimensions and environmental factors",
      color: "text-accent-600",
      bgColor: "bg-accent-50",
    },
  ]

  return (
    <div className="min-h-screen bg-white text-gray-900 overflow-x-hidden font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-lg border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-500 rounded-xl flex items-center justify-center shadow-lg">
                <Sun className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 font-poppins">Rajesh Power Service</h1>
                <p className="text-xs text-primary-600 font-medium">AI-Powered Energy Forecasting</p>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center space-x-8">
              <a
                href="#services"
                className="text-gray-700 hover:text-primary-600 transition-colors duration-300 font-medium"
              >
                Services
              </a>
              <a
                href="#systems"
                className="text-gray-700 hover:text-primary-600 transition-colors duration-300 font-medium"
              >
                Solar Systems
              </a>
              <a
                href="#features"
                className="text-gray-700 hover:text-primary-600 transition-colors duration-300 font-medium"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-gray-700 hover:text-primary-600 transition-colors duration-300 font-medium"
              >
                Pricing
              </a>
              <a href="#about" className="text-gray-700 hover:text-primary-600 transition-colors duration-300 font-medium">
                About
              </a>
              <Button variant="outline" onClick={() => setIsLoginOpen(true)}>
                Login
              </Button>
              <Button>Get Started</Button>
            </div>

            {/* Mobile Menu Button */}
            <button className="lg:hidden text-gray-700" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
            <div className="px-4 py-6 space-y-4">
              <a href="#services" className="block text-gray-700 hover:text-primary-600 transition-colors font-medium">
                Services
              </a>
              <a href="#systems" className="block text-gray-700 hover:text-primary-600 transition-colors font-medium">
                Solar Systems
              </a>
              <a href="#features" className="block text-gray-700 hover:text-primary-600 transition-colors font-medium">
                Features
              </a>
              <a href="#pricing" className="block text-gray-700 hover:text-primary-600 transition-colors font-medium">
                Pricing
              </a>
              <a href="#about" className="block text-gray-700 hover:text-primary-600 transition-colors font-medium">
                About
              </a>
              <Button variant="outline" className="w-full" onClick={() => setIsLoginOpen(true)}>
                Login
              </Button>
              <Button className="w-full">Get Started</Button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center pt-20 bg-gradient-to-br from-primary-50 to-secondary-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div
              className={`space-y-8 transform transition-all duration-1000 ${
                heroVisible ? "translate-x-0 opacity-100" : "-translate-x-20 opacity-0"
              }`}
            >
              <div className="space-y-6">
                <Badge className="bg-primary-100 text-primary-700 border border-primary-200">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Next-Generation AI Forecasting
                </Badge>

                <h1 className="text-5xl lg:text-7xl font-bold leading-tight font-poppins">
                  <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-primary-700 bg-clip-text text-transparent">
                    Future of
                  </span>
                  <br />
                  <span className="text-gray-900">Solar Energy</span>
                  <br />
                  <span className="bg-gradient-to-r from-success-600 to-secondary-600 bg-clip-text text-transparent">
                    Prediction
                  </span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                  Harness the power of advanced artificial intelligence to predict solar energy generation with
                  unprecedented accuracy. Transform your renewable energy operations with our cutting-edge forecasting
                  platform.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <Button size="lg" className="group">
                  <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                  Start Forecasting
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button size="lg" variant="secondary">
                  <Eye className="w-5 h-5 mr-2" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div ref={statsRef} className="grid grid-cols-3 gap-8 pt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 font-poppins">
                    <AnimatedCounter end={99} isVisible={statsVisible} suffix="%" />
                  </div>
                  <div className="text-sm text-gray-600 font-medium">Accuracy Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-success-600 font-poppins">
                    <AnimatedCounter end={1500} isVisible={statsVisible} suffix="+" />
                  </div>
                  <div className="text-sm text-gray-600 font-medium">Plants Analyzed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-secondary-600 font-poppins">
                    <AnimatedCounter end={24} isVisible={statsVisible} />
                    /7
                  </div>
                  <div className="text-sm text-gray-600 font-medium">AI Monitoring</div>
                </div>
              </div>
            </div>

            <div
              className={`relative transform transition-all duration-1000 delay-300 ${
                heroVisible ? "translate-x-0 opacity-100" : "translate-x-20 opacity-0"
              }`}
            >
              <div className="relative h-[600px] w-full">
                <ImageCarousel />
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-8 h-8 text-primary-600" />
        </div>
      </section>

      {/* Solar Systems Section */}
      <section id="systems" className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            ref={servicesRef}
            className={`text-center space-y-6 mb-20 transform transition-all duration-1000 ${
              servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-secondary-100 text-secondary-700 border border-secondary-200">
              <Settings className="w-4 h-4 mr-2" />
              Comprehensive Solar Solutions
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                All Solar Systems
              </span>
              <br />
              <span className="text-gray-900">One Platform</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From residential rooftops to massive solar farms, our AI predicts energy generation across every type of
              solar installation with precision and reliability.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {solarSystems.map((system, index) => (
              <Card
                key={index}
                className={`hover:shadow-xl transition-all duration-500 group transform hover:-translate-y-2 ${
                  servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="p-6 space-y-4">
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${system.gradient} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                  >
                    <system.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 font-poppins">{system.title}</h3>
                  <p className="text-gray-600">{system.description}</p>
                  <ul className="space-y-2">
                    {system.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-500">
                        <CheckCircle className="w-4 h-4 text-success-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            ref={featuresRef}
            className={`text-center space-y-6 mb-20 transform transition-all duration-1000 ${
              featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-success-100 text-success-700 border border-success-200">
              <Cpu className="w-4 h-4 mr-2" />
              Advanced Technology
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-success-600 to-secondary-600 bg-clip-text text-transparent">
                Cutting-Edge
              </span>
              <br />
              <span className="text-gray-900">AI Features</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`group transform transition-all duration-700 ${
                  featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <Card className="hover:shadow-xl transition-all duration-500 h-full group-hover:-translate-y-2">
                  <div className="p-8 space-y-6">
                    <div
                      className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <feature.icon className={`w-8 h-8 ${feature.color}`} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 font-poppins">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-32 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-20">
            <Badge className="bg-primary-100 text-primary-700 border border-primary-200">
              <Brain className="w-4 h-4 mr-2" />
              AI Process
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                How It Works
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our advanced AI platform makes solar energy forecasting simple, accurate, and actionable
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-12">
            {[
              {
                step: "01",
                icon: Database,
                title: "Data Integration",
                description:
                  "Upload your historical generation data, weather patterns, and system specifications. Our platform accepts multiple formats and automatically processes your information with advanced data validation.",
                color: "from-primary-500 to-secondary-500",
              },
              {
                step: "02",
                icon: Brain,
                title: "AI Analysis",
                description:
                  "Our advanced neural networks analyze patterns, correlations, and environmental factors. Machine learning models process millions of data points in real-time using cutting-edge algorithms.",
                color: "from-secondary-500 to-accent-500",
              },
              {
                step: "03",
                icon: BarChart3,
                title: "Precise Forecasting",
                description:
                  "Receive accurate predictions with confidence intervals, risk assessments, and optimization recommendations for maximum energy efficiency and ROI optimization.",
                color: "from-success-500 to-secondary-500",
              },
            ].map((step, index) => (
              <div key={index} className="text-center space-y-6 group">
                <div className="relative">
                  <div
                    className={`w-24 h-24 bg-gradient-to-br ${step.color} rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300 shadow-xl`}
                  >
                    <step.icon className="w-12 h-12 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-sm font-bold text-gray-700">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 font-poppins">{step.title}</h3>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Login Modal */}
      {isLoginOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <div className="p-8 space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-gray-900 font-poppins">Login</h2>
                <button onClick={() => setIsLoginOpen(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="w-6 h-6" />
                </button>
              </div>
              <p className="text-gray-600">Access your solar forecasting dashboard</p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <Input type="password" placeholder="Enter your password" />
                </div>
                <Button className="w-full">Sign In</Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default Home