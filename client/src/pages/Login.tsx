import React from "react";
import { useForm } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { useLoginMutation } from "../service/operations/authAPI";
import Input from "../components/common/Input";
import Password from "../components/common/Password";
import Button from "../components/common/Button";

interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormData>({
    defaultValues: {
      email: "",
      password: "",
    },
  });
  const navigate = useNavigate();
  const { mutate: login, isPending } = useLoginMutation();

  const onSubmit = (data: LoginFormData) => {
    login(data, {
      onSuccess: () => {
        reset();
        navigate("/dashboard");
      },
    });
  };

  return (
    <>
      <h1 className="text-2xl font-semibold text-neutral-900 dark:text-neutral-100 mb-8">
        Login
      </h1>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full flex flex-col gap-5"
      >
        <Input
          label="Email"
          star
          placeholder="<EMAIL>"
          type="email"
          errors={errors.email}
          {...register("email", {
            required: "Email is required",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
              message: "Invalid email address",
            },
          })}
        />
        <Password
          label="Password"
          star
          placeholder="Enter your password"
          errors={errors.password}
          {...register("password", {
            required: "Password is required",
          })}
        />
         <Link
            to="/auth/forgot-password"
            className="text-primary-500 dark:text-primary-400 hover:underline text-end -my-2 text-xs"
          >
            Forgot Password?
          </Link>
        <Button
          type="submit"
          loading={isPending}
          className="w-full"
          variant="primary"
        >
          Login
        </Button>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-3">
          Don’t have an account?{" "}
          <Link
            to="/auth/signup"
            className="text-primary-500 dark:text-primary-400 hover:underline"
          >
            Create account
          </Link>
          .
        </p>
      </form>
    </>
  );
};

export default Login;