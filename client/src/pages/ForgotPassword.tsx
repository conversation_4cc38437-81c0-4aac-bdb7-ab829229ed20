import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import Input from "../components/common/Input";
import Button from "../components/common/Button";
import { useForgotPasswordMutation } from "../service/operations/authAPI";

interface ForgotPasswordFormData {
	email: string;
}

const ForgotPassword: React.FC = () => {
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm<ForgotPasswordFormData>({
		defaultValues: {
			email: "",
		},
	});
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [submittedEmail, setSubmittedEmail] = useState("");
	const [timer, setTimer] = useState(() => {
		const savedTimer = localStorage.getItem("resendTimer");
		return savedTimer ? parseInt(savedTimer, 10) : 50;
	});
	const { mutate: forgotPassword, isPending } = useForgotPasswordMutation();

	useEffect(() => {
		if (isSubmitted && timer > 0) {
			const interval = setInterval(() => {
				setTimer((prev) => {
					const newTimer = prev - 1;
					localStorage.setItem("resendTimer", newTimer.toString());
					return newTimer;
				});
			}, 1000);
			return () => clearInterval(interval);
		} else if (timer === 0) {
			localStorage.removeItem("resendTimer");
		}
	}, [isSubmitted, timer]);

	const onSubmit = (data: ForgotPasswordFormData) => {
		forgotPassword(data, {
			onError: () => {
				setIsSubmitted(true);
				setSubmittedEmail(data.email);
				setTimer(50);
				localStorage.setItem("resendTimer", "50");
				reset();
			},
		});
	};

	const handleResend = () => {
		forgotPassword(
			{ email: submittedEmail },
			{
				onSuccess: () => {
					setTimer(50);
					localStorage.setItem("resendTimer", "50");
				},
			},
		);
	};

	return (
		<>
			<h1 className="text-2xl font-semibold text-neutral-900 dark:text-neutral-100 mb-8">
				Forgot Password
			</h1>
			{!isSubmitted ? (
				<form
					onSubmit={handleSubmit(onSubmit)}
					className="w-full flex flex-col gap-5">
					<Input
						label="Email"
						star
						placeholder="<EMAIL>"
						type="email"
						errors={errors.email}
						{...register("email", {
							required: "Email is required",
							pattern: {
								value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
								message: "Invalid email address",
							},
						})}
					/>
					<Button
						type="submit"
						loading={isPending}
						className="w-full"
						variant="primary">
						Send Reset Email
					</Button>
					<p className="text-sm text-neutral-600 dark:text-neutral-400 mt-3">
						Back to{" "}
						<Link
							to="/auth/login"
							className="text-primary-500 dark:text-primary-400 hover:underline">
							Login
						</Link>
						.
					</p>
				</form>
			) : (
				<div className="w-full flex flex-col gap-5">
					<p className="text-neutral-900 dark:text-neutral-100 text-center">
						We have sent you an email to reset your password.
					</p>
					<p className="text-neutral-600 dark:text-neutral-400 text-center">
						You can resend the email in {timer} seconds.
					</p>
					{timer === 0 && (
						<Button
							onClick={handleResend}
							loading={isPending}
							className="w-full"
							variant="primary">
							Resend Email
						</Button>
					)}
					<p className="text-sm text-neutral-600 dark:text-neutral-400 mt-3 text-center">
						Back to{" "}
						<Link
							to="/auth/login"
							className="text-primary-500 dark:text-primary-400 hover:underline">
							Login
						</Link>
						.
					</p>
				</div>
			)}
		</>
	);
};

export default ForgotPassword;
