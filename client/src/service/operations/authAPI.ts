import axios from "axios";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { userEndpoints } from "../api";
import { formatErrorMessage } from "../../utils/formateErrorMessage";
import { useAppDispatch } from "../../slices/hooks";
import { clearAuth, setAuth } from "../../slices/authSlice";
import { useNavigate } from "react-router-dom";

interface LoginData {
	email: string;
	password: string;
}

interface SignupData {
	firstName: string;
	lastName: string;
	email: string;
	password: string;
	confirmPassword: string;
}

interface ForgotPasswordData {
	email: string;
  }
  

interface LoginResponse {
	success: boolean;
	code: number;
	data: {
		token: string;
		user: { id: string; email: string; displayName: string };
		permissions?: string[] | null;
	};
	message: string;
}

interface SignupResponse {
	success: boolean;
	code: number;
	data: {
		user: { id: string; email: string; displayName: string };
	};
	message: string;
}

interface ForgotPasswordResponse {
	success: boolean;
	code: number;
	message: string;
	data?: any;
  }

interface ErrorParameter {
	name: string;
	location: string;
	value: string;
	message: string;
}

interface ErrorResponse {
	success: false;
	code: number;
	message: string;
	details?: {
		parameters?: ErrorParameter[];
	};
}

interface ProfileResponse {
	success: boolean;
	code: number;
	data: {
		user: { id: string; email: string; displayName: string };
		permissions?: string[];
	};
	message: string;
}

export const useLoginMutation = () => {
	const dispatch = useAppDispatch();
	return useMutation({
		mutationFn: async (data: LoginData) => {
			const response = await axios.post<LoginResponse>(
				userEndpoints.LOGIN_API,
				data,
				{ withCredentials: true }
			);
			if (!response.data.success) {
				throw new Error(response.data.message || "Login failed");
			}
			return response.data;
		},
		onSuccess: (data) => {
			toast.success(data.message || "Logged in successfully!");
			dispatch(
				setAuth({
					token: data.data.token,
					user: data.data.user,
					permissions: data.data.permissions,
				}),
			);
		},
		onError: (error: any) => {
			let errorMessage = "Something went wrong!";

			if (error.response?.data) {
				errorMessage = formatErrorMessage(
					error.response.data as ErrorResponse,
				);
			} else if (error.message) {
				errorMessage = error.message;
			}

			toast.error(errorMessage);
		},
	});
};

export const useSignupMutation = () => {
	return useMutation({
		mutationFn: async (data: SignupData) => {
			const { confirmPassword, ...payload } = data; // Exclude confirmPassword from API payload
			const response = await axios.post<SignupResponse>(
				userEndpoints.SIGNUP_API,
				payload,
			);
			if (!response.data.success) {
				throw new Error(response.data.message || "Signup failed");
			}
			return response.data;
		},
		onSuccess: (data) => {
			toast.success(data.message || "Verification email sent!");
		},
		onError: (error: any) => {
			let errorMessage = "Something went wrong!";
			if (error.response?.data) {
				errorMessage = formatErrorMessage(
					error.response.data as ErrorResponse,
				);
			} else if (error.message) {
				errorMessage = error.message;
			}
			toast.error(errorMessage);
		},
	});
};

export const useGetProfileMutation = () => {
	const dispatch = useAppDispatch();
	const navigate = useNavigate();
	return useMutation({
		mutationFn: async (token: string) => {
			const response = await axios.get<ProfileResponse>(
				userEndpoints.PROFILE_API,
				{
					headers: {
						Authorization: `Bearer ${token}`,
					},
				},
			);
			if (!response.data.success) {
				throw new Error(
					response.data.message || "Failed to fetch profile",
				);
			}
			return response.data;
		},
		onSuccess: (data) => {
			dispatch(
				setAuth({
					token: localStorage.getItem("token")!,
					user: data.data.user,
					permissions: data.data.permissions,
				}),
			);
		},
		onError: (error: any) => {
			const errorMessage =
				error.response?.data?.message ||
				error.message ||
				"Something went wrong!";
			toast.error(errorMessage);
			if (error.response?.status === 401) {
				dispatch(clearAuth());
				navigate("/auth/login");
			}
		},
	});
};



export const useForgotPasswordMutation = () => {
	return useMutation({
	  mutationFn: async (data: ForgotPasswordData) => {
		const response = await axios.post<ForgotPasswordResponse>(
		  userEndpoints.FORGOT_PASSWORD_API,
		  data,
		);
		if (!response.data.success) {
		  throw new Error(response.data.message || "Failed to send reset email");
		}
		return response.data;
	  },
	  onSuccess: (data) => {
		toast.success(data.message || "Reset email sent successfully!");
	  },
	  onError: (error: any) => {
		let errorMessage = "Something went wrong!";
		if (error.response?.data) {
		  errorMessage = formatErrorMessage(
			error.response.data as ErrorResponse,
		  );
		} else if (error.message) {
		  errorMessage = error.message;
		}
		toast.error(errorMessage);
	  },
	});
  };