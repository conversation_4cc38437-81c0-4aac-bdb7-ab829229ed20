import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type Theme = "light" | "dark" | "system";

interface AuthState {
	token: string | null;
	user: { id: string; email: string, displayName: string } | null;
	permissions: string[] | null;
	theme: Theme;
}

const getInitialTheme = (): Theme => {
	const savedTheme = localStorage.getItem("theme") as Theme | null;
	return savedTheme && ["light", "dark", "system"].includes(savedTheme)
		? savedTheme
		: "system";
};
const initialState: AuthState = {
	token: document.cookie ? document.cookie.split("=")[1] : null,
	user: null,
	permissions: null,
	theme: getInitialTheme(),
};

const authSlice = createSlice({
	name: "auth",
	initialState,
	reducers: {
		setAuth: (
			state,
			action: PayloadAction<{
				token: string;
				user: { id: string; email: string, displayName: string };
				permissions?: string[] | null;
			}>,
		) => {
			state.token = action.payload.token;
			state.user = action.payload.user;
			state.permissions = action.payload.permissions || null;
		},
		clearAuth: (state) => {
			state.token = null;
			state.user = null;
			state.permissions = null;
		},
		setTheme: (state, action: PayloadAction<Theme>) => {
			state.theme = action.payload;
			localStorage.setItem("theme", action.payload);
		},
	},
});

export const { setAuth, clearAuth, setTheme } = authSlice.actions;
export default authSlice.reducer;
