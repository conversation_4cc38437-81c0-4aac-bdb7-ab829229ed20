import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ModalState {
  id: string;
  type: string;
  props: Record<string, any>;
  position: { x: number; y: number };
  zIndex: number;
}

interface ModalsState {
  modals: ModalState[];
}

const initialState: ModalsState = {
  modals: [],
};

const modalSlice = createSlice({
  name: "modals",
  initialState,
  reducers: {
    openModal: (
      state,
      action: PayloadAction<{
        id: string;
        type: string;
        props?: Record<string, any>;
        position?: { x: number; y: number };
      }>
    ) => {
      const maxZIndex = state.modals.length
        ? Math.max(...state.modals.map((m) => m.zIndex))
        : 0;
      state.modals.push({
        id: action.payload.id,
        type: action.payload.type,
        props: action.payload.props || {},
        position: action.payload.position || { x: 100, y: 100 },
        zIndex: maxZIndex + 1,
      });
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter((modal) => modal.id !== action.payload);
    },
    updateModalPosition: (
      state,
      action: PayloadAction<{ id: string; position: { x: number; y: number } }>
    ) => {
      const modal = state.modals.find((m) => m.id === action.payload.id);
      if (modal) {
        modal.position = action.payload.position;
      }
    },
    bringToTop: (state, action: PayloadAction<string>) => {
      const modal = state.modals.find((m) => m.id === action.payload);
      if (modal) {
        const maxZIndex = state.modals.length
          ? Math.max(...state.modals.map((m) => m.zIndex))
          : 0;
        modal.zIndex = maxZIndex + 1;
      }
    },
  },
});

export const { openModal, closeModal, updateModalPosition, bringToTop } =
  modalSlice.actions;
export default modalSlice.reducer;