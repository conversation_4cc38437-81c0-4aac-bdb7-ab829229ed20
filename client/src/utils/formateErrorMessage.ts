export interface ErrorParameter {
	name: string;
	location: string;
	value: string;
	message: string;
}

export interface ErrorResponse {
	success: false;
	code: number;
	message: string;
	details?: {
		parameters?: ErrorParameter[];
	};
}

/**
 * Formats an error message for responses with code 4001 or "Invalid request parameters".
 * Combines the main message with parameter-specific error messages if available.
 * @param errorResponse - The error response object from the API.
 * @returns A formatted error message string.
 */
export const formatErrorMessage = (errorResponse: ErrorResponse): string => {
	const { code, message, details } = errorResponse;

	// Handle code 4001 or "Invalid request parameters" message
	if (code === 4001 || message === "Invalid request parameters") {
		if (details?.parameters?.length) {
			const paramErrors = details.parameters
				.map((param) => param.message)
				.join("; ");
			return `${message}: ${paramErrors}`;
		}
	}

	// Return the main message if no specific handling applies
	return message || "Something went wrong!";
};
