import React from "react";
import { Navigate } from "react-router-dom";
import { useAppSelector } from "../../../slices/hooks";

interface PrivateRouteProps {
  children: React.ReactNode;
  permissionsRequired?: string[];
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({
  children,
  permissionsRequired = [],
}) => {
  const { token, permissions } = useAppSelector((state) => state.auth);

  if (!token) {
    return <Navigate to="/auth/login" replace />;
  }

  if (permissionsRequired.length > 0 && permissions) {
    const hasRequiredPermissions = permissionsRequired.every((perm) =>
      permissions.includes(perm)
    );
    if (!hasRequiredPermissions) {
      return <Navigate to="/dashboard" replace />;
    }
  }

  return <>{children}</>;
};

export default PrivateRoute;