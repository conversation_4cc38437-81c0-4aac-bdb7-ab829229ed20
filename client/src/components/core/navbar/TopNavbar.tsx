import React, { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Menu, X, ChevronDown, ChevronUp, LogOut, Settings, Palette } from "lucide-react";
import { useAppDispatch, useAppSelector } from "../../../slices/hooks";
import { clearAuth } from "../../../slices/authSlice";
import { NavItem } from "./navItems";
import favicon from "../../../assets/favicon.png";
import ThemeToggle from "../../common/ThemeToggle";

interface TopNavbarProps {
  topNavItems: NavItem[];
  bottomNavItems: NavItem[];
  className?: string;
}

const TopNavbar: React.FC<TopNavbarProps> = ({ topNavItems, bottomNavItems, className }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeItem, setActiveItem] = useState("");
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleItemClick = (itemName: string) => {
    setActiveItem(activeItem === itemName ? "" : itemName);
  };

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  const handleLogout = () => {
    dispatch(clearAuth());
    navigate("/auth/login");
  };

  const renderNavItems = (items: NavItem[]) => (
    <>
      {items.map((item) => (
        <div key={item.name}>
          {item.children ? (
            <>
              <button
                onClick={() => handleItemClick(item.name)}
                className="flex items-center justify-between w-full p-3 rounded-md text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white transition-colors"
              >
                <div className="flex items-center">
                  <item.icon className="w-6 h-6 mr-3" />
                  <span>{item.name}</span>
                </div>
                {activeItem === item.name ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
              {activeItem === item.name && (
                <div className="ml-8 mt-2 space-y-1 bg-neutral-100 dark:bg-neutral-700 rounded-md">
                  {item.children.map((child) => (
                    <NavLink
                      key={child.name}
                      to={child.path}
                      className={({ isActive }) =>
                        `flex items-center w-full p-3 rounded-md transition-colors ${
                          isActive
                            ? "bg-primary-500 text-white"
                            : "text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white"
                        }`
                      }
                    >
                      <child.icon className="w-6 h-6 mr-3" />
                      {child.name}
                    </NavLink>
                  ))}
                </div>
              )}
            </>
          ) : (
            <NavLink
              to={item.path}
              className={({ isActive }) =>
                `flex items-center w-full p-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary-500 text-white"
                    : "text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white"
                }`
              }
            >
              <item.icon className="w-6 h-6 mr-3" />
              <span>{item.name}</span>
            </NavLink>
          )}
        </div>
      ))}
    </>
  );

  return (
    <nav
      className={`bg-white dark:bg-neutral-800 text-gray-800 dark:text-neutral-100 shadow-md fixed top-0 left-0 right-0 z-50 text-sm ${className || ""}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <img className="w-8 h-8 rounded-md" src={favicon} alt="Favicon" />
          </div>
          <div className="flex items-center">
            <button
              onClick={toggleMenu}
              className="text-gray-700 dark:text-neutral-100 hover:text-primary-500 dark:hover:text-primary-400 transition-colors p-2 rounded-md"
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
            <div className="relative ml-4">
              <button
                onClick={toggleProfileMenu}
                className="flex items-center p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
              >
                <img
                  src={`https://api.dicebear.com/9.x/initials/svg?seed=${user?.displayName || "Hariom Patidar"}`}
                  alt="User Avatar"
                  className="w-8 h-8 rounded-full"
                />
                {isProfileMenuOpen ? <ChevronUp className="ml-2" size={16} /> : <ChevronDown className="ml-2" size={16} />}
              </button>
              {isProfileMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-800 shadow-lg rounded-md py-2 z-50">
                  <NavLink
                    to="/settings"
                    className="flex items-center w-full p-3 text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white transition-colors"
                  >
                    <Settings className="w-5 h-5 mr-3" />
                    Settings
                  </NavLink>
                  <div className="flex items-center w-full p-3 text-gray-700 dark:text-neutral-100">
                    <Palette className="w-5 h-5 mr-3" />
                    <ThemeToggle />
                  </div>
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full p-3 text-red-600 dark:text-red-400 hover:bg-red-600 hover:text-white transition-colors"
                  >
                    <LogOut className="w-5 h-5 mr-3" />
                    Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {isMenuOpen && (
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white dark:bg-neutral-800 shadow-lg">
          {renderNavItems(topNavItems)}
          <div className="border-t border-neutral-200 dark:border-neutral-700 my-2" />
          {renderNavItems(bottomNavItems)}
        </div>
      )}
    </nav>
  );
};

export default TopNavbar;