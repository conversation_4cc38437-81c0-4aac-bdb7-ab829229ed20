import React, { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import {
	ChevronLeft,
	ChevronRight,
	ChevronDown,
	ChevronUp,
	LogOut,
	Settings,
	Palette,
  User,
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "../../../slices/hooks";
import { clearAuth } from "../../../slices/authSlice";
import { NavItem } from "./navItems";
import logo from "../../../assets/logo.png";
import favicon from "../../../assets/favicon.png";
import ThemeToggle from "../../common/ThemeToggle";

interface SidebarProps {
	isOpen: boolean;
	setIsOpen: (isOpen: boolean) => void;
	topNavItems: NavItem[];
	bottomNavItems: NavItem[];
	className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
	isOpen,
	setIsOpen,
	topNavItems,
	bottomNavItems,
	className,
}) => {
	const [activeItem, setActiveItem] = useState("");
	const { user } = useAppSelector((state) => state.auth);
	const dispatch = useAppDispatch();
	const navigate = useNavigate();

	const toggleSidebar = () => setIsOpen(!isOpen);

	const handleItemClick = (itemName: string) => {
		setActiveItem(activeItem === itemName ? "" : itemName);
	};

	const handleLogout = () => {
		dispatch(clearAuth());
		navigate("/auth/login");
	};

	const renderNavItems = (items: NavItem[]) => (
		<>
			{items.map((item) => (
				<div key={item.name}>
					{item.children ? (
						<>
							<button
								onClick={() => handleItemClick(item.name)}
								className={`flex items-center w-full ${
									isOpen ? "p-3" : "p-2"
								} text-gray-700 dark:text-neutral-100 rounded-md hover:bg-primary-500 hover:text-white transition-all`}>
								<item.icon
									className={`w-6 h-6 ${
										isOpen ? "mr-3" : "mx-auto"
									}`}
								/>
								{isOpen && (
									<div className="w-full flex justify-between">
										<span>{item.name}</span>
										{activeItem === item.name ? (
											<ChevronUp className="w-5 h-5" />
										) : (
											<ChevronDown className="w-5 h-5" />
										)}
									</div>
								)}
							</button>
							{isOpen && activeItem === item.name && (
								<div className="ml-8 my-2 flex flex-col gap-1">
									{item.children.map((child) => (
										<NavLink
											key={child.name}
											to={child.path}
											className={({ isActive }) =>
												`flex items-center w-full p-3 bg-neutral-100 dark:bg-neutral-700 rounded-md ${
													isActive
														? "bg-primary-500 text-white"
														: "text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white"
												} transition-colors`
											}>
											<child.icon className="w-5 h-5 mr-3" />
											{child.name}
										</NavLink>
									))}
								</div>
							)}
						</>
					) : (
						<NavLink
							to={item.path}
							className={({ isActive }) =>
								`flex items-center w-full ${
									isOpen ? "p-3" : "p-2"
								} rounded-md ${
									isActive
										? "bg-primary-500 text-white"
										: "text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white"
								} transition-colors ${
									isOpen
										? ""
										: "justify-center max-w-max mx-auto"
								}`
							}>
							<item.icon
								className={`w-6 h-6 ${
									isOpen ? "mr-3" : "mx-auto"
								}`}
							/>
							{isOpen && <span>{item.name}</span>}
						</NavLink>
					)}
				</div>
			))}
		</>
	);

	return (
		<div
			className={`fixed z-50 left-0 top-0 h-screen bg-white dark:bg-neutral-800 text-gray-800 dark:text-neutral-100 shadow-lg text-sm transition-all duration-300 ease-in-out ${
				isOpen ? "w-64" : "w-20"
			} flex flex-col ${className || ""}`}>
			<div
				className={`flex items-center ${
					isOpen ? "justify-between" : "justify-center"
				} p-4 border-b border-neutral-200 dark:border-neutral-700`}>
				{isOpen ? (
					<img className="h-10 rounded-md" src={logo} alt="Logo" />
				) : (
					<img
						className="w-10 h-10 rounded-md"
						src={favicon}
						alt="Favicon"
					/>
				)}
				<button
					onClick={toggleSidebar}
					className={`p-2 rounded-full transition-colors ${
						isOpen
							? "text-primary-500 dark:text-primary-400 hover:bg-primary-500 hover:text-white"
							: "absolute -right-4 top-6 bg-primary-500 text-white shadow-md"
					}`}>
					{isOpen ? (
						<ChevronLeft size={20} />
					) : (
						<ChevronRight size={20} />
					)}
				</button>
			</div>

			<div className="flex flex-col justify-between h-full overflow-auto">
				<nav className="flex flex-col gap-1 p-4">
					{renderNavItems(topNavItems)}
				</nav>
				<nav className="flex flex-col gap-1 p-4">
					{renderNavItems(bottomNavItems)}
				</nav>
			</div>

			<div className="border-t border-neutral-200 dark:border-neutral-700">
				<div className="group relative">
					<div
						className={`flex items-center w-full ${
							isOpen ? "p-3" : "p-2"
						} rounded-md text-gray-700 dark:text-neutral-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors ${
							isOpen ? "" : "justify-center max-w-max mx-auto"
						}`}>
						<img
							src={`https://api.dicebear.com/9.x/initials/svg?seed=${
								user?.displayName || "Hariom Patidar"
							}`}
							alt="User Avatar"
							className="w-8 h-8 rounded-full"
						/>
						{isOpen && (
							<div className="ml-3">
								<span className="font-medium">
									{user?.displayName || "Hariom Patidar"}
								</span>
								<span className="block text-xs text-neutral-500 dark:text-neutral-400">
									{user?.email || "<EMAIL>"}
								</span>
							</div>
						)}
					</div>
					<div className="absolute -right-60 bottom-8 w-60 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-md py-2 z-50 hidden group-hover:block">
						<NavLink
							to="/profile"
							className="flex items-center w-full p-3 text-gray-700 dark:text-neutral-100 hover:bg-primary-500 hover:text-white transition-colors">
							<User className="w-5 h-5 mr-3" />
							Profile
						</NavLink>
						<div className="flex items-center w-full p-3 text-gray-700 dark:text-neutral-100">
							<Palette className="w-5 h-5 mr-3" />
							<ThemeToggle />
						</div>
						<button
							onClick={handleLogout}
							className="flex items-center w-full p-3 text-red-500 hover:bg-red-600 hover:text-white transition-colors">
							<LogOut className="w-5 h-5 mr-3" />
							Logout
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default Sidebar;
