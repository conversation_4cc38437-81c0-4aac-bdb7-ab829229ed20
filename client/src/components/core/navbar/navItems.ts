import { Home, Bell, Settings, Users, Factory } from "lucide-react";

export interface NavItem {
	name: string;
	path: string;
	icon: React.ComponentType<{ className?: string }>;
	children?: NavItem[];
}

export const topNavItems: NavItem[] = [
	{
		name: "Dashboard",
		path: "/dashboard",
		icon: Home,
	},
  {
		name: "Plants",
		path: "/plants",
		icon: Factory,
	},
	{
		name: "Settings",
		path: "/dashboard/settings",
		icon: Settings,
		children: [
			{
				name: "Profile",
				path: "/dashboard/settings/profile",
				icon: Users,
			},
			{
				name: "Preferences",
				path: "/dashboard/settings/preferences",
				icon: Settings,
			},
		],
	},
];

export const bottomNavItems: NavItem[] = [
	{
		name: "Notifications",
		path: "/notifications",
		icon: Bell,
	},
  {
		name: "Settings",
		path: "/settings",
		icon: Settings,
	},
];
