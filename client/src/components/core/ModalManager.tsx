import React from "react";
import { useSelector, useDispatch } from "react-redux";
import DraggableModal from "../common/DraggableModal";
import { RootState } from "../../slices/store";
import { closeModal } from "../../slices/modelSlice";

interface ModalType {
  [key: string]: React.ComponentType<any>;
}

const modalTypes: ModalType = {
  confirmation: DraggableModal,
  // Add more modal types here, e.g., form: FormModal
};

const ModalManager: React.FC = () => {
  const dispatch = useDispatch();
  const modals = useSelector((state: RootState) => state.modals.modals);

  return (
    <>
      {modals.map((modal) => {
        const ModalComponent = modalTypes[modal.type] || DraggableModal;
        return (
          <ModalComponent
            key={modal.id}
            id={modal.id}
            onClose={() => dispatch(closeModal(modal.id))}
            initialPosition={modal.position}
            zIndex={modal.zIndex}
            {...modal.props}
          />
        );
      })}
    </>
  );
};

export default ModalManager;