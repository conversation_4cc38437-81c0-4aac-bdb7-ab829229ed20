import React, { ForwardRefExoticComponent, RefAttributes } from "react";
import { useId } from "react";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  star?: boolean;
  className?: string;
  labelClassName?: string;
  name?: string;
  $id?: string;
  errors?: { message?: string };
  divClassName?: string;
}

const Input: ForwardRefExoticComponent<InputProps & RefAttributes<HTMLInputElement>> = React.forwardRef(
  (
    {
      label = "",
      star,
      type = "text",
      className = "",
      labelClassName = "",
      name = "",
      $id,
      errors,
      divClassName = "",
      ...props
    },
    ref
  ) => {
    const id = useId();

    return (
      <div className={`flex flex-col gap-1 w-full ${divClassName}`}>
        {label && (
          <label
            className={`text-neutral-700 dark:text-neutral-200 font-medium ${labelClassName}`}
            htmlFor={$id || id}
          >
            {label}
            {star && <sup className="text-error-500 ml-1">*</sup>}
          </label>
        )}
        <input
          name={name}
          type={type}
          className={`w-full px-3 py-2 border rounded-md bg-transparent text-neutral-900 dark:text-neutral-100 border-neutral-300 dark:border-neutral-600 hover:border-primary-500 dark:hover:border-primary-400 focus:border-primary-500 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-200 dark:focus:ring-primary-700 transition-colors disabled:bg-neutral-100 dark:disabled:bg-neutral-800 disabled:text-neutral-500 dark:disabled:text-neutral-400 outline-none ${className}`}
          ref={ref}
          id={$id || id}
          {...props}
        />
        {errors?.message && (
          <span className="mt-1 text-xs text-error-500 dark:text-error-400">
            {errors.message}
          </span>
        )}
      </div>
    );
  }
);

export default Input;