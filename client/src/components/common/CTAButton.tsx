import React from "react";
import { Link } from "react-router-dom";

interface CTAButtonProps {
  text: string;
  link: string;
  className?: string;
  variant?: "primary" | "secondary";
  size?: "sm" | "md" | "lg";
}

const CTAButton: React.FC<CTAButtonProps> = ({
  text,
  link,
  className = "",
  variant = "primary",
  size = "md",
}) => {
  const baseStyles = "flex items-center justify-center font-medium rounded-md transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variantStyles = {
    primary: "bg-primary-500 dark:bg-primary-600 text-white hover:bg-primary-600 dark:hover:bg-primary-700 focus:ring-primary-300 dark:focus:ring-primary-800",
    secondary: "bg-secondary-500 dark:bg-secondary-600 text-white hover:bg-secondary-600 dark:hover:bg-secondary-700 focus:ring-secondary-300 dark:focus:ring-secondary-800",
  };
  const sizeStyles = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  };

  return (
    <Link
      to={link}
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}
    >
      {text}
    </Link>
  );
};

export default CTAButton;