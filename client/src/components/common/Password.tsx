import React, { ForwardRefExoticComponent, RefAttributes, useState } from "react";
import { useId } from "react";
import { Eye, EyeOff } from "lucide-react";

interface PasswordProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  star?: boolean;
  className?: string;
  labelClassName?: string;
  name?: string;
  $id?: string;
  errors?: { message?: string };
}

const Password: ForwardRefExoticComponent<PasswordProps & RefAttributes<HTMLInputElement>> = React.forwardRef(
  (
    {
      label = "",
      star,
      className = "",
      labelClassName = "",
      name = "",
      $id,
      errors,
      ...props
    },
    ref
  ) => {
    const id = useId();
    const [showPassword, setShowPassword] = useState(false);

    return (
      <div className="flex flex-col gap-1 w-full">
        {label && (
          <label
            className={`text-neutral-700 dark:text-neutral-200 font-medium ${labelClassName}`}
            htmlFor={$id || id}
          >
            {label}
            {star && <sup className="text-error-500 ml-1">*</sup>}
          </label>
        )}
        <div className="relative">
          <input
            name={name}
            type={showPassword ? "text" : "password"}
            className={`w-full px-3 py-2 pr-10 border rounded-md bg-transparent text-neutral-900 dark:text-neutral-100 border-neutral-300 dark:border-neutral-600 hover:border-primary-500 dark:hover:border-primary-400 focus:border-primary-500 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-200 dark:focus:ring-primary-700 transition-colors disabled:bg-neutral-100 dark:disabled:bg-neutral-800 disabled:text-neutral-500 dark:disabled:text-neutral-400 outline-none ${className}`}
            ref={ref}
            id={$id || id}
            {...props}
          />
          <button
            type="button"
            onClick={() => setShowPassword((prev) => !prev)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400"
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors?.message && (
          <span className="mt-1 text-xs text-error-500 dark:text-error-400">
            {errors.message}
          </span>
        )}
      </div>
    );
  }
);

export default Password;