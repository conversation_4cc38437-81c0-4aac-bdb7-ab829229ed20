import React, { useRef, useState, useEffect } from "react";
import { X } from "lucide-react";
import { useDispatch } from "react-redux";
import Button from "./Button";
import { bringToTop, updateModalPosition } from "../../slices/modelSlice";

interface DraggableModalProps {
  id: string;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  onClose: () => void;
  initialPosition?: { x: number; y: number };
  zIndex: number;
}

const DraggableModal: React.FC<DraggableModalProps> = ({
  id,
  title,
  children,
  footer,
  className = "",
  contentClassName = "",
  onClose,
  initialPosition = { x: 100, y: 100 },
  zIndex,
}) => {
  const dispatch = useDispatch();
  const modalRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState(initialPosition);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Ensure modal stays within screen boundaries
  const constrainPosition = (x: number, y: number) => {
    if (!modalRef.current) return { x, y };
    const modalWidth = modalRef.current.offsetWidth;
    const modalHeight = modalRef.current.offsetHeight;
    const maxX = window.innerWidth - modalWidth;
    const maxY = window.innerHeight - modalHeight;
    return {
      x: Math.max(0, Math.min(x, maxX)),
      y: Math.max(0, Math.min(y, maxY)),
    };
  };

  // Handle mouse down to start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (modalRef.current) {
      setIsDragging(true);
      const rect = modalRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      dispatch(bringToTop(id)); // Bring to top on click
    }
  };

  // Handle mouse move to update position
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && modalRef.current) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      const constrained = constrainPosition(newX, newY);
      setPosition(constrained);
      dispatch(updateModalPosition({ id, position: constrained }));
    }
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add global event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    }
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  // Bring modal to top when clicked anywhere
  const handleClick = () => {
    dispatch(bringToTop(id));
  };

  return (
    <div
      ref={modalRef}
      className={`absolute z-${zIndex} bg-neutral-50 dark:bg-neutral-800 rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[80vh] overflow-y-auto ${className}`}
      style={{ top: position.y, left: position.x }}
      onClick={handleClick}
    >
      <div
        className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700 cursor-move"
        onMouseDown={handleMouseDown}
      >
        {title && (
          <h2 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            {title}
          </h2>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          className="p-1"
          aria-label="Close modal"
        >
          <X size={16} />
        </Button>
      </div>
      <div className={`p-4 ${contentClassName}`}>{children}</div>
      {footer && (
        <div className="flex justify-end gap-2 p-4 border-t border-neutral-200 dark:border-neutral-700">
          {footer}
        </div>
      )}
    </div>
  );
};

export default DraggableModal;