import React from "react";

interface SpinnerProps {
  className?: string;
  size?: string | number;
  color?: string;
}

const Spinner: React.FC<SpinnerProps> = ({ className = "", size = "5", color = "primary-500" }) => {
  return (
    <div
      className={`relative flex items-center justify-center ${className}`}
      style={{ width: `${size}rem`, height: `${size}rem` }}
      aria-label="Loading"
    >
      <div
        className={`animate-spin rounded-full border-2 border-t-${color} dark:border-t-${color} border-neutral-300 dark:border-neutral-600 absolute inset-0`}
        style={{ borderWidth: "0.1rem" }}
      />
    </div>
  );
};

export default Spinner;