import React, { ForwardRefExoticComponent, RefAttributes } from "react";

interface ToggleProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  star?: boolean;
  className?: string;
  labelClassName?: string;
  errors?: { message?: string };
  divClassName?: string;
}

const Toggle: ForwardRefExoticComponent<ToggleProps & RefAttributes<HTMLInputElement>> = React.forwardRef(
  (
    {
      label = "",
      star,
      className = "",
      labelClassName = "",
      errors,
      divClassName = "",
      ...props
    },
    ref
  ) => {
    return (
      <div className={`flex flex-col gap-1 w-full ${divClassName}`}>
        {label && (
          <label
            className={`text-neutral-700 dark:text-neutral-200 font-medium flex items-center gap-2 ${labelClassName}`}
          >
            {label}
            {star && <sup className="text-error-500">*</sup>}
            <input
              type="checkbox"
              className={`relative appearance-none w-12 h-6 rounded-full bg-neutral-300 dark:bg-neutral-600 checked:bg-primary-500 dark:checked:bg-primary-400 cursor-pointer transition-colors focus:outline-none focus:ring-2 focus:ring-primary-200 dark:focus:ring-primary-700 ${className}`}
              ref={ref}
              {...props}
            />
          </label>
        )}
        {errors?.message && (
          <span className="mt-1 text-xs text-error-500 dark:text-error-400">
            {errors.message}
          </span>
        )}
      </div>
    );
  }
);

export default Toggle;