import React, { ForwardRefExoticComponent, RefAttributes, useEffect, useState } from "react";
import { useId } from "react";
import toast from "react-hot-toast";
import Select, { MultiValue, SingleValue } from "react-select";

interface Option {
  value: string;
  label: string;
}

interface AsyncSelectProps {
  label?: string;
  star?: boolean;
  className?: string;
  labelClassName?: string;
  name?: string;
  $id?: string;
  errors?: { message?: string };
  divClassName?: string;
  loadOptions: () => Promise<Option[]>;
  isMulti?: boolean;
  placeholder?: string;
  defaultValue?: Option | Option[] | null;
  isClearable?: boolean;
  cacheOptions?: boolean;
  onChange?: (value: SingleValue<Option> | MultiValue<Option>) => void;
}

const AsyncSelect: ForwardRefExoticComponent<AsyncSelectProps & RefAttributes<Select>> = React.forwardRef(
  (
    {
      label = "",
      star,
      className = "",
      labelClassName = "",
      name = "",
      $id,
      errors,
      divClassName = "",
      loadOptions,
      isMulti = false,
      placeholder = "Select...",
      defaultValue = null,
      isClearable = true,
      cacheOptions = true,
      onChange,
      ...props
    },
    ref
  ) => {
    const id = useId();
    const [options, setOptions] = useState<Option[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      const fetchOptions = async () => {
        try {
          setLoading(true);
          const data = await loadOptions();
          setOptions(data);
        } catch (error) {
          toast.error("Error loading options");
        } finally {
          setLoading(false);
        }
      };
      fetchOptions();
    }, [loadOptions]);

    const customStyles = {
      control: (provided: any) => ({
        ...provided,
        backgroundColor: "transparent",
        borderColor: errors?.message
          ? "#DC2626"
          : "var(--tw-neutral-300, #D4D4D4)",
        borderRadius: "0.375rem",
        padding: "0.5rem 0.75rem",
        boxShadow: "none",
        "&:hover": {
          borderColor: "var(--tw-primary-500, #0055FF)",
        },
        "&:focus-within": {
          borderColor: "var(--tw-primary-500, #0055FF)",
          boxShadow: "0 0 0 2px var(--tw-primary-200, #CCDDFF)",
        },
      }),
      menu: (provided: any) => ({
        ...provided,
        backgroundColor: "var(--tw-neutral-50, #F5F5F5)",
        borderRadius: "0.375rem",
        marginTop: "0.25rem",
      }),
      option: (provided: any, state: any) => ({
        ...provided,
        backgroundColor: state.isSelected
          ? "var(--tw-primary-100, #CCDDFF)"
          : state.isFocused
          ? "var(--tw-neutral-100, #E5E5E5)"
          : "transparent",
        color: "var(--tw-neutral-900, #111827)",
        "&:hover": {
          backgroundColor: "var(--tw-neutral-100, #E5E5E5)",
        },
      }),
      singleValue: (provided: any) => ({
        ...provided,
        color: "var(--tw-neutral-900, #111827)",
      }),
      placeholder: (provided: any) => ({
        ...provided,
        color: "var(--tw-neutral-500, #6B7280)",
      }),
    };

    const darkStyles = {
      control: (provided: any) => ({
        ...provided,
        backgroundColor: "transparent",
        borderColor: errors?.message
          ? "#EF4444"
          : "var(--tw-neutral-600, #4B5563)",
        "&:hover": {
          borderColor: "var(--tw-primary-400, #3377FF)",
        },
        "&:focus-within": {
          borderColor: "var(--tw-primary-400, #3377FF)",
          boxShadow: "0 0 0 2px var(--tw-primary-700, #003399)",
        },
      }),
      menu: (provided: any) => ({
        ...provided,
        backgroundColor: "var(--tw-neutral-800, #1F2937)",
      }),
      option: (provided: any, state: any) => ({
        ...provided,
        backgroundColor: state.isSelected
          ? "var(--tw-primary-900, #001133)"
          : state.isFocused
          ? "var(--tw-neutral-700, #374151)"
          : "transparent",
        color: "var(--tw-neutral-100, #F3F4F6)",
        "&:hover": {
          backgroundColor: "var(--tw-neutral-700, #374151)",
        },
      }),
      singleValue: (provided: any) => ({
        ...provided,
        color: "var(--tw-neutral-100, #F3F4F6)",
      }),
      placeholder: (provided: any) => ({
        ...provided,
        color: "var(--tw-neutral-400, #9CA3AF)",
      }),
    };

    return (
      <div className={`flex flex-col gap-1 w-full ${divClassName}`}>
        {label && (
          <label
            className={`text-neutral-700 dark:text-neutral-200 font-medium ${labelClassName}`}
            htmlFor={$id || id}
          >
            {label}
            {star && <sup className="text-error-500 ml-1 dark:text-error-400">*</sup>}
          </label>
        )}
        <Select
          name={name}
          options={options}
          isMulti={isMulti}
          classNamePrefix="react-select"
          className={`react-select-container ${className}`}
          placeholder={placeholder}
          defaultValue={defaultValue}
          isClearable={isClearable}
        //   cacheOptions={cacheOptions}
        //   ref={ref}
          onChange={onChange}
          isLoading={loading}
          styles={{
            ...customStyles,
            ...(document.documentElement.classList.contains("dark") ? darkStyles : {}),
          }}
          {...props}
        />
        {errors?.message && (
          <span className="mt-1 text-xs text-error-500 dark:text-error-400">
            {errors.message}
          </span>
        )}
      </div>
    );
  }
);

export default AsyncSelect;