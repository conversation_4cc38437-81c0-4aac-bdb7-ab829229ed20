import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, File, X } from "lucide-react";

interface FileUploadProps {
  label?: string;
  star?: boolean;
  accept?: Record<string, string[]>;
  multiple?: boolean;
  onChange: (files: File[]) => void;
  value?: File[];
  errorMessage?: string;
  existingFiles?: { fileName: string }[];
  removeExistingFiles?: (file: { fileName: string }) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  label,
  star,
  accept,
  multiple = false,
  onChange,
  value = [],
  errorMessage,
  existingFiles = [],
  removeExistingFiles,
}) => {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      onChange(multiple ? [...value, ...acceptedFiles] : acceptedFiles);
    },
    [onChange, multiple, value]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    multiple,
  });

  const removeFile = (fileToRemove: File) => {
    onChange(value.filter((file) => file !== fileToRemove));
  };

  return (
    <div className="space-y-2 w-full">
      {label && (
        <label className="text-neutral-700 dark:text-neutral-200 font-medium">
          {label}
          {star && <sup className="text-error-500 ml-1">*</sup>}
        </label>
      )}
      <div
        {...getRootProps()}
        className={`p-6 border-2 border-dashed rounded-lg transition-colors ${
          isDragActive
            ? "border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/20"
            : "border-neutral-300 dark:border-neutral-600"
        } ${errorMessage ? "border-error-500 dark:border-error-400" : ""}`}
      >
        <input {...getInputProps()} />
        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-500" />
          <p className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
            Drag &apos;n&apos; drop files here, or click to select files
          </p>
        </div>
      </div>
      {errorMessage && (
        <span className="mt-1 text-xs text-error-500 dark:text-error-400">
          {errorMessage}
        </span>
      )}
      {existingFiles.length > 0 && (
        <div className="mt-2">
          <p className="text-neutral-600 dark:text-neutral-400">Existing files:</p>
          <ul className="mt-2 space-y-2">
            {existingFiles.map((file, index) => (
              <li
                key={index}
                className="flex items-center justify-between p-2 bg-neutral-100 dark:bg-neutral-800 rounded-md"
              >
                <div className="flex items-center">
                  <File className="mr-2 text-neutral-500 dark:text-neutral-400" size={16} />
                  <span className="text-sm text-neutral-700 dark:text-neutral-200">
                    {file.fileName}
                  </span>
                </div>
                {removeExistingFiles && (
                  <button
                    type="button"
                    onClick={() => removeExistingFiles(file)}
                    className="text-error-500 dark:text-error-400 hover:text-error-600 dark:hover:text-error-300"
                  >
                    <X size={16} />
                  </button>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
      {value.length > 0 && (
        <div className="mt-2">
          <p className="text-neutral-600 dark:text-neutral-400">Selected files:</p>
          <ul className="mt-2 space-y-2">
            {value.map((file, index) => (
              <li
                key={index}
                className="flex items-center justify-between p-2 bg-neutral-100 dark:bg-neutral-800 rounded-md"
              >
                <div className="flex items-center">
                  <File className="mr-2 text-neutral-500 dark:text-neutral-400" size={16} />
                  <span className="text-sm text-neutral-700 dark:text-neutral-200">
                    {file.name}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(file)}
                  className="text-error-500 dark:text-error-400 hover:text-error-600 dark:hover:text-error-300"
                >
                  <X size={16} />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileUpload;