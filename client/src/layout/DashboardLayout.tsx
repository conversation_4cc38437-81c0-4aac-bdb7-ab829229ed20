import React, { useState } from "react";
import { Outlet } from "react-router-dom";
import { useMediaQuery } from "usehooks-ts";
import Sidebar from "../components/core/navbar/Sidebar";
import TopNavbar from "../components/core/navbar/TopNavbar";
import { topNavItems, bottomNavItems } from "../components/core/navbar/navItems";

const DashboardLayout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const isLargeScreen = useMediaQuery("(min-width: 768px)");

  return (
    <div className="min-h-screen bg-neutral-100 dark:bg-neutral-900">
      {isLargeScreen ? (
        <Sidebar
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          topNavItems={topNavItems}
          bottomNavItems={bottomNavItems}
          className="hidden md:flex"
        />
      ) : (
        <TopNavbar
          topNavItems={topNavItems}
          bottomNavItems={bottomNavItems}
          className="md:hidden"
        />
      )}
      <main
        className={`transition-all duration-300 ease-in-out ${
          isLargeScreen ? (isSidebarOpen ? "md:ml-64" : "md:ml-16") : "mt-16"
        } p-4`}
      >
        <Outlet />
      </main>
    </div>
  );
};

export default DashboardLayout;