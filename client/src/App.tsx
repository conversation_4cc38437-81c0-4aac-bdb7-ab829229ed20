import React, { useEffect } from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import { routesConfig, RouteConfig } from "./routes/routesConfig";
import OpenRoute from "./components/core/auth/OpenRoute";
import PrivateRoute from "./components/core/auth/PrivateRoute";
import { useGetProfileMutation } from "./service/operations/authAPI";
import NotFound from "./pages/NotFound";
import logo from "./assets/logo.png";
import { useAppSelector } from "./slices/hooks";

const App: React.FC = () => {
  const { token, user, theme } = useAppSelector((state) => state.auth);
  const { mutate: getProfile, isPending: isProfileLoading } = useGetProfileMutation();

  useEffect(() => {
    const root = document.documentElement;
    if (theme === "system") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
      root.classList.toggle("dark", prefersDark);
    } else {
      root.classList.toggle("dark", theme === "dark");
    }
  }, []);

  useEffect(() => {
    if (token && !user) {
      getProfile(token);
    }
  }, [token, user, getProfile]);

  if (isProfileLoading || (token && !user)) {
    return (
      <div className="flex justify-center items-center h-screen w-screen">
        <img
          src={logo}
          alt="Logo"
          loading="eager"
          className="w-48 animate-pulse"
        />
      </div>
    );
  }

  const renderRoutes = (routes: RouteConfig[]) => {
    return routes.map((route) => {
      if (route.isNeutral) {
        return (
          <Route
            key={route.path}
            path={route.path}
            element={
              typeof route.element !== "string" ? <route.element /> : null
            }
          />
        );
      }

      if (route.children) {
        return (
          <Route
            key={route.path}
            path={route.path}
            element={
              route.isPublic ? (
                <OpenRoute>
                  {typeof route.element === "string" ? (
                    React.createElement(
                      route.element === "HomeLayout"
                        ? require("./layout/HomeLayout").default
                        : require("./layout/DashboardLayout").default
                    )
                  ) : (
                    <route.element />
                  )}
                </OpenRoute>
              ) : (
                <PrivateRoute permissionsRequired={route.permissionsRequired || []}>
                  {typeof route.element === "string" ? (
                    React.createElement(
                      route.element === "HomeLayout"
                        ? require("./layout/HomeLayout").default
                        : require("./layout/DashboardLayout").default
                    )
                  ) : (
                    <route.element />
                  )}
                </PrivateRoute>
              )
            }
          >
            {renderRoutes(route.children)}
          </Route>
        );
      }

      return (
        <Route
          key={route.path}
          path={route.path}
          element={
            route.isPublic ? (
              <OpenRoute>
                {typeof route.element !== "string" ? <route.element /> : null}
              </OpenRoute>
            ) : (
              <PrivateRoute permissionsRequired={route.permissionsRequired || []}>
                {typeof route.element !== "string" ? <route.element /> : null}
              </PrivateRoute>
            )
          }
        />
      );
    });
  };

  return (
    <div className="w-full h-full min-h-screen bg-neutral-50 dark:bg-neutral-900 text-black dark:text-white text-sm">
      <Routes>
        <Route
          path="/auth"
          element={
            <OpenRoute>
              <Navigate to="/auth/login" replace />
            </OpenRoute>
          }
        />
        {renderRoutes(routesConfig)}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </div>
  );
};

export default App;