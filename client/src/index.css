@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.font-inter {
  font-family: "Inter", sans-serif;
}

.font-poppins {
  font-family: "Poppins", sans-serif;
}

/* Custom scrollbar - Futuristic */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #0f172a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #044994, #14b8a6);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(4, 73, 148, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #033a75, #0d9488);
  box-shadow: 0 0 15px rgba(4, 73, 148, 0.8);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom button hover effects */
.btn-hover {
  position: relative;
  overflow: hidden;
}

.btn-hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover:hover::before {
  left: 100%;
}

/* Futuristic AI Effects */
.neural-network {
  background: radial-gradient(circle at 20% 50%, rgba(4, 73, 148, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(236, 28, 36, 0.1) 0%, transparent 50%);
}

.holographic {
  background: linear-gradient(45deg, transparent 30%, rgba(4, 73, 148, 0.1) 50%, transparent 70%);
  background-size: 20px 20px;
  animation: data-flow 3s linear infinite;
}

.cyber-grid {
  background-image:
    linear-gradient(rgba(4, 73, 148, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(4, 73, 148, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.neon-border {
  border: 1px solid rgba(4, 73, 148, 0.3);
  box-shadow:
    0 0 5px rgba(4, 73, 148, 0.2),
    inset 0 0 5px rgba(4, 73, 148, 0.1);
}

.neon-border:hover {
  border-color: rgba(4, 73, 148, 0.6);
  box-shadow:
    0 0 20px rgba(4, 73, 148, 0.4),
    inset 0 0 20px rgba(4, 73, 148, 0.2);
}

.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.data-stream {
  position: relative;
  overflow: hidden;
}

.data-stream::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #044994, transparent);
  animation: data-flow 2s linear infinite;
}

.ai-pulse {
  position: relative;
}

.ai-pulse::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(4, 73, 148, 0.3);
  border-radius: inherit;
  transform: translate(-50%, -50%);
  animation: neural-pulse 2s ease-in-out infinite;
}

/* Matrix rain effect */
.matrix-bg {
  position: relative;
  overflow: hidden;
}

.matrix-bg::before {
  content: "01010101010101010101010101010101010101010101";
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 200%;
  color: rgba(4, 73, 148, 0.1);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 20px;
  animation: matrix-rain 10s linear infinite;
  pointer-events: none;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #EC1C24;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #044994;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

/* Particle effect */
.particles {
  position: relative;
  overflow: hidden;
}

.particles::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(4, 73, 148, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(20, 184, 166, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(236, 28, 36, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(4, 73, 148, 0.3), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float 20s linear infinite;
  pointer-events: none;
}