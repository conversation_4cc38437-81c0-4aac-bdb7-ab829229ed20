import React from "react";
import Login from "../pages/Login";
import Dashboard from "../pages/Dashboard";
import Home from "../pages/Home";
import HomeLayout from "../layout/HomeLayout";
import DashboardLayout from "../layout/DashboardLayout";
import Signup from "../pages/Signup";
import ForgotPassword from "../pages/ForgotPassword";

export interface RouteConfig {
  path: string;
  element: React.ComponentType | string;
  isNeutral?: boolean;
  isPublic?: boolean;
  permissionsRequired?: string[];
  children?: RouteConfig[];
}

export const routesConfig: RouteConfig[] = [
  {
    path: "/",
    element: Home,
    isNeutral: true,
    isPublic: false,
    permissionsRequired: [],
    children: undefined,
  },
  {
    path: "/auth",
    element: HomeLayout,
    isNeutral: false,
    isPublic: true,
    permissionsRequired: [],
    children: [
      {
        path: "login",
        element: Login,
        isNeutral: false,
        isPublic: true,
        permissionsRequired: [],
        children: undefined,
      },
      {
        path: "signup",
        element: Signup,
        isNeutral: false,
        isPublic: true,
        permissionsRequired: [],
        children: undefined,
      },
      {
        path: "forgot-password",
        element: ForgotPassword,
        isNeutral: false,
        isPublic: true,
        permissionsRequired: [],
        children: undefined,
      },
    ],
  },
  {
    path: "/dashboard",
    element: DashboardLayout,
    isNeutral: false,
    isPublic: false,
    permissionsRequired: [],
    children: [
      {
        path: "",
        element: Dashboard,
        isNeutral: false,
        isPublic: false,
        permissionsRequired: [],
        children: undefined,
      },
    ],
  },
];