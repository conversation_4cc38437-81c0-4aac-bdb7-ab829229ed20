{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.76.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.17", "@types/node": "^16.18.126", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "axios": "^1.9.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "typescript": "^4.9.5", "usehooks-ts": "^3.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}