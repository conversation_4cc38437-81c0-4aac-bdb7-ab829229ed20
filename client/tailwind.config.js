/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: 'class', // Enables dark mode with 'dark' class
  theme: {
    extend: {
      fontFamily: {
        inter: ["Inter", "sans-serif"],
        poppins: ["Poppins", "sans-serif"],
      },
      colors: {
        primary: {
          25: '#F5F8FF',
          50: '#E6EEFF',
          100: '#CCDDFF',
          200: '#99BBFF',
          300: '#6699FF',
          400: '#3377FF',
          500: '#0055FF', // Base primary color
          600: '#0044CC',
          700: '#003399',
          800: '#002266',
          900: '#001133',
        },
        secondary: {
          25: '#F8F5FF',
          50: '#EDE6FF',
          100: '#D6CCFF',
          200: '#AD99FF',
          300: '#8466FF',
          400: '#5B33FF',
          500: '#3200FF', // Base secondary color
          600: '#2800CC',
          700: '#1E0099',
          800: '#140066',
          900: '#0A0033',
        },
        accent: {
          25: '#FFF5F8',
          50: '#FFE6ED',
          100: '#FFCCDB',
          200: '#FF99B7',
          300: '#FF6693',
          400: '#FF336F',
          500: '#FF004B', // Base accent color
          600: '#CC003C',
          700: '#99002D',
          800: '#66001E',
          900: '#33000F',
        },
        neutral: {
          25: '#FAFAFA',
          50: '#F5F5F5',
          100: '#E5E5E5',
          200: '#D4D4D4',
          300: '#A3A3A3',
          400: '#737373',
          500: '#525252', // Base neutral color
          600: '#424242',
          700: '#313131',
          800: '#212121',
          900: '#101010',
        },
        success: {
          25: '#F0FFF4',
          50: '#DCFCE7',
          100: '#BBF7D0',
          200: '#86EFAC',
          300: '#4ADE80',
          400: '#22C55E',
          500: '#16A34A', // Base success color
          600: '#15803D',
          700: '#166534',
          800: '#14532D',
          900: '#0D3D1F',
        },
        error: {
          25: '#FFF1F2',
          50: '#FFE4E6',
          100: '#FECDD3',
          200: '#FDA4AF',
          300: '#F87171',
          400: '#EF4444',
          500: '#DC2626', // Base error color
          600: '#B91C1C',
          700: '#991B1B',
          800: '#7F1D1D',
          900: '#5E1414',
        },
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-20px)" },
        },
        glow: {
          "0%, 100%": { boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)" },
          "50%": { boxShadow: "0 0 40px rgba(59, 130, 246, 0.6)" },
        },
        "gradient-shift": {
          "0%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
          "100%": { backgroundPosition: "0% 50%" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        float: "float 6s ease-in-out infinite",
        glow: "glow 2s ease-in-out infinite",
        "gradient-shift": "gradient-shift 3s ease infinite",
      },
    },
  },
  plugins: [],
};