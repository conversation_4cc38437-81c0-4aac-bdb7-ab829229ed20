/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: 'class', // Enables dark mode with 'dark' class
  theme: {
    extend: {
      fontFamily: {
        inter: ["Inter", "sans-serif"],
        poppins: ["Poppins", "sans-serif"],
      },
      colors: {
        // Rajesh Power Brand Colors
        brand: {
          blue: {
            50: '#E8F2FF',
            100: '#D1E5FF',
            200: '#A3CBFF',
            300: '#75B1FF',
            400: '#4797FF',
            500: '#044994', // Main brand blue
            600: '#033A75',
            700: '#022B56',
            800: '#021C38',
            900: '#010E1C',
          },
          red: {
            50: '#FEF2F2',
            100: '#FEE2E2',
            200: '#FECACA',
            300: '#FCA5A5',
            400: '#F87171',
            500: '#EC1C24', // Main brand red
            600: '#DC2626',
            700: '#B91C1C',
            800: '#991B1B',
            900: '#7F1D1D',
          },
        },
        // AI/Tech focused colors
        primary: {
          25: '#F0F9FF',
          50: '#E0F2FE',
          100: '#BAE6FD',
          200: '#7DD3FC',
          300: '#38BDF8',
          400: '#0EA5E9',
          500: '#044994', // Using brand blue as primary
          600: '#033A75',
          700: '#022B56',
          800: '#021C38',
          900: '#010E1C',
        },
        secondary: {
          25: '#FEFEFE',
          50: '#F8FAFC',
          100: '#F1F5F9',
          200: '#E2E8F0',
          300: '#CBD5E1',
          400: '#94A3B8',
          500: '#64748B', // Neutral tech gray
          600: '#475569',
          700: '#334155',
          800: '#1E293B',
          900: '#0F172A',
        },
        accent: {
          25: '#FEF2F2',
          50: '#FEE2E2',
          100: '#FECACA',
          200: '#FCA5A5',
          300: '#F87171',
          400: '#EF4444',
          500: '#EC1C24', // Using brand red as accent
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
        },
        // AI/Futuristic colors
        cyber: {
          50: '#F0FDFF',
          100: '#CCFBF1',
          200: '#99F6E4',
          300: '#5EEAD4',
          400: '#2DD4BF',
          500: '#14B8A6',
          600: '#0D9488',
          700: '#0F766E',
          800: '#115E59',
          900: '#134E4A',
        },
        neon: {
          50: '#ECFDF5',
          100: '#D1FAE5',
          200: '#A7F3D0',
          300: '#6EE7B7',
          400: '#34D399',
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
        },
        neutral: {
          25: '#FAFAFA',
          50: '#F5F5F5',
          100: '#E5E5E5',
          200: '#D4D4D4',
          300: '#A3A3A3',
          400: '#737373',
          500: '#525252', // Base neutral color
          600: '#424242',
          700: '#313131',
          800: '#212121',
          900: '#101010',
        },
        success: {
          25: '#F0FFF4',
          50: '#DCFCE7',
          100: '#BBF7D0',
          200: '#86EFAC',
          300: '#4ADE80',
          400: '#22C55E',
          500: '#16A34A', // Base success color
          600: '#15803D',
          700: '#166534',
          800: '#14532D',
          900: '#0D3D1F',
        },
        error: {
          25: '#FFF1F2',
          50: '#FFE4E6',
          100: '#FECDD3',
          200: '#FDA4AF',
          300: '#F87171',
          400: '#EF4444',
          500: '#DC2626', // Base error color
          600: '#B91C1C',
          700: '#991B1B',
          800: '#7F1D1D',
          900: '#5E1414',
        },
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-20px)" },
        },
        glow: {
          "0%, 100%": { boxShadow: "0 0 20px rgba(4, 73, 148, 0.3)" },
          "50%": { boxShadow: "0 0 40px rgba(4, 73, 148, 0.6)" },
        },
        "glow-red": {
          "0%, 100%": { boxShadow: "0 0 20px rgba(236, 28, 36, 0.3)" },
          "50%": { boxShadow: "0 0 40px rgba(236, 28, 36, 0.6)" },
        },
        "glow-cyber": {
          "0%, 100%": { boxShadow: "0 0 20px rgba(20, 184, 166, 0.4)" },
          "50%": { boxShadow: "0 0 40px rgba(20, 184, 166, 0.8)" },
        },
        "gradient-shift": {
          "0%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
          "100%": { backgroundPosition: "0% 50%" },
        },
        "pulse-slow": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.5" },
        },
        "bounce-slow": {
          "0%, 100%": { transform: "translateY(-25%)", animationTimingFunction: "cubic-bezier(0.8, 0, 1, 1)" },
          "50%": { transform: "translateY(0)", animationTimingFunction: "cubic-bezier(0, 0, 0.2, 1)" },
        },
        "spin-slow": {
          from: { transform: "rotate(0deg)" },
          to: { transform: "rotate(360deg)" },
        },
        "matrix-rain": {
          "0%": { transform: "translateY(-100%)" },
          "100%": { transform: "translateY(100vh)" },
        },
        "data-flow": {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        "neural-pulse": {
          "0%, 100%": { transform: "scale(1)", opacity: "0.7" },
          "50%": { transform: "scale(1.1)", opacity: "1" },
        },
        "hologram": {
          "0%, 100%": { transform: "rotateY(0deg)" },
          "50%": { transform: "rotateY(180deg)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        float: "float 6s ease-in-out infinite",
        glow: "glow 2s ease-in-out infinite",
        "glow-red": "glow-red 2s ease-in-out infinite",
        "glow-cyber": "glow-cyber 2s ease-in-out infinite",
        "gradient-shift": "gradient-shift 3s ease infinite",
        "pulse-slow": "pulse-slow 4s ease-in-out infinite",
        "bounce-slow": "bounce-slow 3s ease-in-out infinite",
        "spin-slow": "spin-slow 8s linear infinite",
        "matrix-rain": "matrix-rain 3s linear infinite",
        "data-flow": "data-flow 2s linear infinite",
        "neural-pulse": "neural-pulse 2s ease-in-out infinite",
        "hologram": "hologram 4s ease-in-out infinite",
      },
    },
  },
  plugins: [],
};