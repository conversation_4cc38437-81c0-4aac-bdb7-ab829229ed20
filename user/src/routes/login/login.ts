import express, { NextFunction, Request } from "express";
import { loginValidation } from "./login.validation";
import { responseHandler, validateRequest } from "../../middleware";
import { NotFoundError } from "../../utils/error";

const router = express.Router();

router.post(
	"/v1/login",
	responseHandler,
	loginValidation,
	validateRequest,
	async (req: Request, res: any, next: NextFunction) => {
		// throw new NotFoundError("Not found error");

		res.sendResponse({
			message: "hello",
		}, 201, {});
	},
);

export { router as loginV1Router };
