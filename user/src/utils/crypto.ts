// import * as crypto from 'crypto';

// // Configuration
// const ALGORITHM = 'aes-256-gcm';
// const IV_LENGTH = 12; // GCM recommends 12 bytes
// const SALT_LENGTH = 16; // For key derivation
// const PBKDF2_ITERATIONS = 100_000;
// const KEY_LENGTH = 32; // 256 bits for AES-256

// /**
//  * Encrypts data using AES-256-GCM
//  * @param data Data to encrypt (string or JSON-serializable object)
//  * @param secret Secret key for encryption
//  * @returns Base64-encoded ciphertext (iv:salt:authTag:ciphertext)
//  * @throws Error if encryption fails
//  */
// export async function encryptData<T>(data: T, secret: string): Promise<string> {
//   try {
//     // Convert data to string
//     const plaintext = typeof data === 'string' ? data : JSON.stringify(data);

//     // Generate random IV and salt
//     const iv = crypto.randomBytes(IV_LENGTH);
//     const salt = crypto.randomBytes(SALT_LENGTH);

//     // Derive key using PBKDF2
//     const key = crypto.pbkdf2Sync(secret, salt, PBKDF2_ITERATIONS, KEY_LENGTH, 'sha256');

//     // Create cipher
//     const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

//     // Encrypt data
//     let encrypted = cipher.update(plaintext, 'utf8', 'base64');
//     encrypted += cipher.final('base64');

//     // Get authentication tag
//     const authTag = cipher.getAuthTag();

//     // Combine components: iv:salt:authTag:ciphertext
//     return `${iv.toString('base64')}:${salt.toString('base64')}:${authTag.toString('base64')}:${encrypted}`;
//   } catch (error) {
//     throw new Error(`Encryption failed: ${(error as Error).message}`);
//   }
// }

// /**
//  * Decrypts data encrypted with AES-256-GCM
//  * @param encryptedData Base64-encoded ciphertext (iv:salt:authTag:ciphertext)
//  * @param secret Secret key for decryption
//  * @returns Decrypted data (parsed as JSON if originally an object)
//  * @throws Error if decryption fails
//  */
// export async function decryptData<T>(encryptedData: string, secret: string): Promise<T> {
//   try {
//     // Split combined string
//     const [ivB64, saltB64, authTagB64, ciphertext] = encryptedData.split(':');
//     if (!ivB64 || !saltB64 || !authTagB64 || !ciphertext) {
//       throw new Error('Invalid encrypted data format');
//     }

//     // Decode components
//     const iv = Buffer.from(ivB64, 'base64');
//     const salt = Buffer.from(saltB64, 'base64');
//     const authTag = Buffer.from(authTagB64, 'base64');

//     // Derive key using PBKDF2
//     const key = crypto.pbkdf2Sync(secret, salt, PBKDF2_ITERATIONS, KEY_LENGTH, 'sha256');

//     // Create decipher
//     const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
//     decipher.setAuthTag(authTag);

//     // Decrypt data
//     let decrypted = decipher.update(ciphertext, 'base64', 'utf8');
//     decrypted += decipher.final('utf8');

//     // Parse JSON if original data was an object
//     try {
//       return JSON.parse(decrypted) as T;
//     } catch {
//       return decrypted as T; // Return as string if not JSON
//     }
//   } catch (error) {
//     throw new Error(`Decryption failed: ${(error as Error).message}`);
//   }
// }