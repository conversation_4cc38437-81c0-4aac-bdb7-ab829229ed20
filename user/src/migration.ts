import { Sequelize, QueryInterface } from "sequelize";
import { config } from "dotenv";
import { Umzug, SequelizeStorage } from "umzug";

config();

const database: string = process.env.DB_NAME!;
const username: string = process.env.DB_USER!;
const password: string = process.env.DB_PASSWORD!;
const host: string = process.env.DB_HOST!;

const sequelize: Sequelize = new Sequelize(database, username, password, {
  host: host,
  dialect: "postgres",
});

const umzug: Umzug<QueryInterface> = new Umzug({
  migrations: {
    glob: "migrations/*.ts",
  },
  context: sequelize.getQueryInterface(),
  storage: new SequelizeStorage({ sequelize }),
  logger: console,
});

(async () => {
  try {
    await umzug.up();
    console.log("Migrations performed successfully.");
  } catch (error) {
    console.error("Error running migrations:", error);
  }
})();