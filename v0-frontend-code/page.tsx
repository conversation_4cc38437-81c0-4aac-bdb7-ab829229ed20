"use client"

import { useState, useEffect, useRef } from "react"
import {
  ArrowRight,
  BarChart3,
  Brain,
  Database,
  Sun,
  Shield,
  Cpu,
  Satellite,
  Waves,
  Leaf,
  Building,
  Factory,
  Home,
  MapPin,
  Battery,
  Grid3X3,
  Lightbulb,
  Droplets,
  Menu,
  X,
  ChevronDown,
  Play,
  Globe,
  Target,
  Eye,
  Sparkles,
  Rocket,
  Layers,
  Settings,
  Activity,
  Users,
  Award,
  TrendingUp,
  CheckCircle,
  Star,
  Quote,
  Mail,
  Phone,
  LocateIcon as Location,
  Calendar,
  Download,
  Share2,
  Send,
} from "lucide-react"

// Custom hook for intersection observer
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [options])

  return [ref, isIntersecting]
}

// Animated Counter Component
const AnimatedCounter = ({ end, duration = 2000, isVisible, suffix = "" }) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    let startTime = null
    const animate = (currentTime) => {
      if (startTime === null) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      setCount(Math.floor(progress * end))

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    requestAnimationFrame(animate)
  }, [end, duration, isVisible])

  return (
    <span>
      {count}
      {suffix}
    </span>
  )
}

// Auto-scrolling Image Carousel
const ImageCarousel = () => {
  const images = [
    {
      url: "/placeholder.svg?height=600&width=800",
      text: "Advanced Solar Forecasting",
      subtitle: "AI-Powered Predictions",
    },
    {
      url: "/placeholder.svg?height=600&width=800",
      text: "Real-time Analytics",
      subtitle: "Live Performance Monitoring",
    },
    { url: "/placeholder.svg?height=600&width=800", text: "Smart Energy Solutions", subtitle: "Optimized Generation" },
    {
      url: "/placeholder.svg?height=600&width=800",
      text: "Predictive Intelligence",
      subtitle: "Future-Ready Technology",
    },
    { url: "/placeholder.svg?height=600&width=800", text: "Renewable Energy Future", subtitle: "Sustainable Tomorrow" },
  ]

  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [images.length])

  return (
    <div className="relative h-full w-full overflow-hidden rounded-2xl shadow-2xl">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
            index === currentIndex ? "opacity-100 scale-100" : "opacity-0 scale-110"
          }`}
        >
          <div className="h-full w-full bg-cover bg-center relative" style={{ backgroundImage: `url(${image.url})` }}>
            <div className="absolute inset-0 bg-gradient-to-t from-blue-900/80 via-blue-600/40 to-transparent" />
            <div className="absolute bottom-8 left-8 right-8">
              <h3 className="text-4xl font-bold text-white mb-2 font-poppins tracking-wide">{image.text}</h3>
              <p className="text-xl text-blue-100 font-inter">{image.subtitle}</p>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mt-4" />
            </div>
          </div>
        </div>
      ))}

      {/* Indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex ? "bg-blue-400 scale-125" : "bg-white/50"
            }`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}

// Button Component
const Button = ({ children, variant = "primary", size = "md", className = "", onClick, ...props }) => {
  const baseClasses =
    "inline-flex items-center justify-center font-semibold transition-all duration-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2"

  const variants = {
    primary:
      "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl focus:ring-blue-500",
    secondary: "bg-white border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500",
    outline: "border-2 border-gray-300 text-gray-700 hover:border-blue-600 hover:text-blue-600 focus:ring-blue-500",
    ghost: "text-gray-600 hover:text-blue-600 hover:bg-blue-50 focus:ring-blue-500",
  }

  const sizes = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
  }

  return (
    <button className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`} onClick={onClick} {...props}>
      {children}
    </button>
  )
}

// Card Component
const Card = ({ children, className = "", ...props }) => {
  return (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-100 ${className}`} {...props}>
      {children}
    </div>
  )
}

// Badge Component
const Badge = ({ children, className = "", ...props }) => {
  return (
    <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${className}`} {...props}>
      {children}
    </span>
  )
}

// Input Component
const Input = ({ className = "", ...props }) => {
  return (
    <input
      className={`w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 ${className}`}
      {...props}
    />
  )
}

export default function RajeshPowerPlatform() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isLoginOpen, setIsLoginOpen] = useState(false)
  const [scrollY, setScrollY] = useState(0)

  // Intersection observer refs
  const [heroRef, heroVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [servicesRef, servicesVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [statsRef, statsVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [featuresRef, featuresVisible] = useIntersectionObserver({ threshold: 0.1 })
  const [testimonialsRef, testimonialsVisible] = useIntersectionObserver({ threshold: 0.1 })

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const solarSystems = [
    {
      icon: Home,
      title: "Residential Rooftop",
      description: "Smart home solar solutions with AI-powered optimization for maximum efficiency",
      features: ["Smart monitoring", "Cost optimization", "Performance analytics"],
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: Building,
      title: "Commercial Rooftop",
      description: "Enterprise-grade solar installations for businesses and commercial buildings",
      features: ["Enterprise monitoring", "ROI tracking", "Energy management"],
      gradient: "from-purple-500 to-pink-500",
    },
    {
      icon: Factory,
      title: "Ground Mount Farms",
      description: "Large-scale solar farms with precision forecasting and optimization",
      features: ["Utility-scale monitoring", "Grid integration", "Performance optimization"],
      gradient: "from-green-500 to-emerald-500",
    },
    {
      icon: Waves,
      title: "Floating Solar",
      description: "Innovative floatovoltaic systems on water bodies with specialized monitoring",
      features: ["Water surface monitoring", "Environmental tracking", "Efficiency optimization"],
      gradient: "from-cyan-500 to-blue-500",
    },
    {
      icon: Leaf,
      title: "Agrivoltaics",
      description: "Dual-use solar and agriculture optimization for sustainable farming",
      features: ["Crop monitoring", "Dual-use optimization", "Agricultural analytics"],
      gradient: "from-lime-500 to-green-500",
    },
    {
      icon: Grid3X3,
      title: "BIPV Systems",
      description: "Building-integrated photovoltaic solutions seamlessly integrated into architecture",
      features: ["Architectural integration", "Aesthetic optimization", "Building analytics"],
      gradient: "from-orange-500 to-red-500",
    },
    {
      icon: Satellite,
      title: "Solar Tracking",
      description: "Dynamic tracking systems for maximum sun exposure and energy generation",
      features: ["Sun tracking", "Dynamic optimization", "Movement analytics"],
      gradient: "from-indigo-500 to-purple-500",
    },
    {
      icon: Battery,
      title: "Hybrid Systems",
      description: "Solar + storage integrated solutions for 24/7 energy availability",
      features: ["Storage optimization", "Grid balancing", "Energy arbitrage"],
      gradient: "from-yellow-500 to-orange-500",
    },
    {
      icon: MapPin,
      title: "Off-Grid Solar",
      description: "Remote area solar installations with autonomous operation capabilities",
      features: ["Remote monitoring", "Autonomous operation", "Reliability tracking"],
      gradient: "from-teal-500 to-cyan-500",
    },
    {
      icon: Lightbulb,
      title: "Solar Street Lighting",
      description: "Smart street lighting with solar power and intelligent control systems",
      features: ["Smart controls", "Motion sensing", "Energy efficiency"],
      gradient: "from-amber-500 to-yellow-500",
    },
    {
      icon: Droplets,
      title: "Solar Water Pumping",
      description: "Agricultural water pumping solutions with weather-based optimization",
      features: ["Weather integration", "Pump optimization", "Water management"],
      gradient: "from-blue-500 to-teal-500",
    },
    {
      icon: Target,
      title: "PM Kusum Scheme",
      description: "Government scheme solar installations with compliance monitoring",
      features: ["Compliance tracking", "Subsidy optimization", "Government reporting"],
      gradient: "from-rose-500 to-pink-500",
    },
  ]

  const features = [
    {
      icon: Brain,
      title: "Advanced AI Models",
      description: "Deep learning algorithms trained on millions of data points for unprecedented accuracy",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      icon: Activity,
      title: "Real-time Analytics",
      description: "Live monitoring and instant performance insights with millisecond response times",
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      icon: Shield,
      title: "99.5% Accuracy",
      description: "Industry-leading prediction accuracy with continuous learning and model improvement",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      icon: Rocket,
      title: "Instant Processing",
      description: "Lightning-fast analysis and forecasting results delivered in real-time",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      icon: Globe,
      title: "Global Weather Data",
      description: "Integrated satellite and meteorological data sources from around the world",
      color: "text-cyan-600",
      bgColor: "bg-cyan-50",
    },
    {
      icon: Layers,
      title: "Multi-layer Analysis",
      description: "Comprehensive analysis across multiple data dimensions and environmental factors",
      color: "text-pink-600",
      bgColor: "bg-pink-50",
    },
  ]

  const testimonials = [
    {
      name: "Dr. Rajesh Kumar",
      position: "Chief Technology Officer",
      company: "SolarTech Industries",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "Rajesh Power Service has revolutionized our solar forecasting capabilities. Their AI models have improved our prediction accuracy by 40% and helped us optimize our energy production significantly.",
    },
    {
      name: "Priya Sharma",
      position: "Operations Manager",
      company: "Green Energy Solutions",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "The platform's real-time analytics and forecasting have been game-changing for our solar farm operations. We've seen a 25% increase in efficiency since implementing their solution.",
    },
    {
      name: "Amit Patel",
      position: "Renewable Energy Consultant",
      company: "EcoVision Consulting",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "Outstanding service and cutting-edge technology. The AI-powered predictions have helped our clients make better investment decisions and optimize their solar installations.",
    },
  ]

  const pricingPlans = [
    {
      name: "Starter",
      price: "₹9,999",
      period: "/month",
      description: "Perfect for small residential installations",
      features: [
        "Up to 5 solar installations",
        "Basic AI forecasting",
        "Daily predictions",
        "Email support",
        "Mobile app access",
      ],
      popular: false,
    },
    {
      name: "Professional",
      price: "₹24,999",
      period: "/month",
      description: "Ideal for commercial and medium-scale projects",
      features: [
        "Up to 50 solar installations",
        "Advanced AI forecasting",
        "Hourly predictions",
        "Priority support",
        "API access",
        "Custom reports",
        "Weather integration",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "For large-scale solar farms and utilities",
      features: [
        "Unlimited installations",
        "Premium AI models",
        "Real-time predictions",
        "24/7 dedicated support",
        "Full API access",
        "Custom integrations",
        "Advanced analytics",
        "White-label solution",
      ],
      popular: false,
    },
  ]

  return (
    <div className="min-h-screen bg-white text-gray-900 overflow-x-hidden font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-lg border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                <Sun className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 font-poppins">Rajesh Power Service</h1>
                <p className="text-xs text-blue-600 font-medium">AI-Powered Energy Forecasting</p>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center space-x-8">
              <a
                href="#services"
                className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium"
              >
                Services
              </a>
              <a
                href="#systems"
                className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium"
              >
                Solar Systems
              </a>
              <a
                href="#features"
                className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium"
              >
                Features
              </a>
              <a
                href="#pricing"
                className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium"
              >
                Pricing
              </a>
              <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">
                About
              </a>
              <Button variant="outline" onClick={() => setIsLoginOpen(true)}>
                Login
              </Button>
              <Button>Get Started</Button>
            </div>

            {/* Mobile Menu Button */}
            <button className="lg:hidden text-gray-700" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
            <div className="px-4 py-6 space-y-4">
              <a href="#services" className="block text-gray-700 hover:text-blue-600 transition-colors font-medium">
                Services
              </a>
              <a href="#systems" className="block text-gray-700 hover:text-blue-600 transition-colors font-medium">
                Solar Systems
              </a>
              <a href="#features" className="block text-gray-700 hover:text-blue-600 transition-colors font-medium">
                Features
              </a>
              <a href="#pricing" className="block text-gray-700 hover:text-blue-600 transition-colors font-medium">
                Pricing
              </a>
              <a href="#about" className="block text-gray-700 hover:text-blue-600 transition-colors font-medium">
                About
              </a>
              <Button variant="outline" className="w-full" onClick={() => setIsLoginOpen(true)}>
                Login
              </Button>
              <Button className="w-full">Get Started</Button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center pt-20 bg-gradient-to-br from-blue-50 to-cyan-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div
              className={`space-y-8 transform transition-all duration-1000 ${
                heroVisible ? "translate-x-0 opacity-100" : "-translate-x-20 opacity-0"
              }`}
            >
              <div className="space-y-6">
                <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Next-Generation AI Forecasting
                </Badge>

                <h1 className="text-5xl lg:text-7xl font-bold leading-tight font-poppins">
                  <span className="bg-gradient-to-r from-blue-600 via-cyan-600 to-blue-700 bg-clip-text text-transparent">
                    Future of
                  </span>
                  <br />
                  <span className="text-gray-900">Solar Energy</span>
                  <br />
                  <span className="bg-gradient-to-r from-green-600 to-cyan-600 bg-clip-text text-transparent">
                    Prediction
                  </span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                  Harness the power of advanced artificial intelligence to predict solar energy generation with
                  unprecedented accuracy. Transform your renewable energy operations with our cutting-edge forecasting
                  platform.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <Button size="lg" className="group">
                  <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                  Start Forecasting
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button size="lg" variant="secondary">
                  <Eye className="w-5 h-5 mr-2" />
                  Watch Demo
                </Button>
              </div>

              {/* Stats */}
              <div ref={statsRef} className="grid grid-cols-3 gap-8 pt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 font-poppins">
                    <AnimatedCounter end={99} isVisible={statsVisible} suffix="%" />
                  </div>
                  <div className="text-sm text-gray-600 font-medium">Accuracy Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 font-poppins">
                    <AnimatedCounter end={1500} isVisible={statsVisible} suffix="+" />
                  </div>
                  <div className="text-sm text-gray-600 font-medium">Plants Analyzed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 font-poppins">
                    <AnimatedCounter end={24} isVisible={statsVisible} />
                    /7
                  </div>
                  <div className="text-sm text-gray-600 font-medium">AI Monitoring</div>
                </div>
              </div>
            </div>

            <div
              className={`relative transform transition-all duration-1000 delay-300 ${
                heroVisible ? "translate-x-0 opacity-100" : "translate-x-20 opacity-0"
              }`}
            >
              <div className="relative h-[600px] w-full">
                <ImageCarousel />
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-8 h-8 text-blue-600" />
        </div>
      </section>

      {/* Solar Systems Section */}
      <section id="systems" className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            ref={servicesRef}
            className={`text-center space-y-6 mb-20 transform transition-all duration-1000 ${
              servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-purple-100 text-purple-700 border border-purple-200">
              <Settings className="w-4 h-4 mr-2" />
              Comprehensive Solar Solutions
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                All Solar Systems
              </span>
              <br />
              <span className="text-gray-900">One Platform</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From residential rooftops to massive solar farms, our AI predicts energy generation across every type of
              solar installation with precision and reliability.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {solarSystems.map((system, index) => (
              <Card
                key={index}
                className={`hover:shadow-xl transition-all duration-500 group transform hover:-translate-y-2 ${
                  servicesVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="p-6 space-y-4">
                  <div
                    className={`w-16 h-16 bg-gradient-to-br ${system.gradient} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                  >
                    <system.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 font-poppins">{system.title}</h3>
                  <p className="text-gray-600">{system.description}</p>
                  <ul className="space-y-2">
                    {system.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-500">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            ref={featuresRef}
            className={`text-center space-y-6 mb-20 transform transition-all duration-1000 ${
              featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
            }`}
          >
            <Badge className="bg-green-100 text-green-700 border border-green-200">
              <Cpu className="w-4 h-4 mr-2" />
              Advanced Technology
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-green-600 to-cyan-600 bg-clip-text text-transparent">
                Cutting-Edge
              </span>
              <br />
              <span className="text-gray-900">AI Features</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`group transform transition-all duration-700 ${
                  featuresVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <Card className="hover:shadow-xl transition-all duration-500 h-full group-hover:-translate-y-2">
                  <div className="p-8 space-y-6">
                    <div
                      className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <feature.icon className={`w-8 h-8 ${feature.color}`} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 font-poppins">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-32 bg-gradient-to-br from-blue-50 to-cyan-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-20">
            <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
              <Brain className="w-4 h-4 mr-2" />
              AI Process
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                How It Works
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our advanced AI platform makes solar energy forecasting simple, accurate, and actionable
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-12">
            {[
              {
                step: "01",
                icon: Database,
                title: "Data Integration",
                description:
                  "Upload your historical generation data, weather patterns, and system specifications. Our platform accepts multiple formats and automatically processes your information with advanced data validation.",
                color: "from-blue-500 to-cyan-500",
              },
              {
                step: "02",
                icon: Brain,
                title: "AI Analysis",
                description:
                  "Our advanced neural networks analyze patterns, correlations, and environmental factors. Machine learning models process millions of data points in real-time using cutting-edge algorithms.",
                color: "from-purple-500 to-pink-500",
              },
              {
                step: "03",
                icon: BarChart3,
                title: "Precise Forecasting",
                description:
                  "Receive accurate predictions with confidence intervals, risk assessments, and optimization recommendations for maximum energy efficiency and ROI optimization.",
                color: "from-green-500 to-cyan-500",
              },
            ].map((step, index) => (
              <div key={index} className="text-center space-y-6 group">
                <div className="relative">
                  <div
                    className={`w-24 h-24 bg-gradient-to-br ${step.color} rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300 shadow-xl`}
                  >
                    <step.icon className="w-12 h-12 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-sm font-bold text-gray-700">{step.step}</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 font-poppins">{step.title}</h3>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section ref={testimonialsRef} className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-20">
            <Badge className="bg-yellow-100 text-yellow-700 border border-yellow-200">
              <Users className="w-4 h-4 mr-2" />
              Customer Success
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                What Our Clients
              </span>
              <br />
              <span className="text-gray-900">Say About Us</span>
            </h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className={`hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${
                  testimonialsVisible ? "translate-y-0 opacity-100" : "translate-y-20 opacity-0"
                }`}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <div className="p-8 space-y-6">
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <Quote className="w-8 h-8 text-blue-600" />
                  <p className="text-gray-600 leading-relaxed italic">"{testimonial.text}"</p>
                  <div className="flex items-center space-x-4">
                    <img
                      src={testimonial.image || "/placeholder.svg"}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                      <p className="text-sm text-gray-600">{testimonial.position}</p>
                      <p className="text-sm text-blue-600">{testimonial.company}</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-32 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-20">
            <Badge className="bg-green-100 text-green-700 border border-green-200">
              <TrendingUp className="w-4 h-4 mr-2" />
              Pricing Plans
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Choose Your
              </span>
              <br />
              <span className="text-gray-900">Perfect Plan</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible pricing options designed to scale with your solar energy operations
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`relative hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${
                  plan.popular ? "ring-2 ring-blue-600 scale-105" : ""
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-4 py-2">
                      <Award className="w-4 h-4 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <div className="p-8 space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-gray-900 font-poppins">{plan.name}</h3>
                    <p className="text-gray-600 mt-2">{plan.description}</p>
                    <div className="mt-6">
                      <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                      <span className="text-gray-600">{plan.period}</span>
                    </div>
                  </div>
                  <ul className="space-y-4">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full ${plan.popular ? "bg-gradient-to-r from-blue-600 to-blue-700" : ""}`}
                    variant={plan.popular ? "primary" : "outline"}
                  >
                    {plan.name === "Enterprise" ? "Contact Sales" : "Get Started"}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-32 bg-gradient-to-br from-gray-900 to-blue-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <Badge className="bg-white/20 text-white border border-white/30">
              <Building className="w-4 h-4 mr-2" />
              About Us
            </Badge>
            <h2 className="text-4xl lg:text-6xl font-bold font-poppins">
              <span className="text-white">Rajesh Power Service</span>
              <br />
              <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Limited</span>
            </h2>
            <p className="text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              We are pioneers in renewable energy forecasting, combining decades of industry expertise with cutting-edge
              artificial intelligence to help solar energy producers optimize their operations and maximize efficiency.
              Our mission is to accelerate the global transition to sustainable energy.
            </p>

            <div className="grid md:grid-cols-4 gap-8 pt-12">
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-cyan-400 font-poppins">15+</div>
                <div className="text-blue-100">Years Experience</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-cyan-400 font-poppins">2000+</div>
                <div className="text-blue-100">MW Forecasted</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-cyan-400 font-poppins">100+</div>
                <div className="text-blue-100">Enterprise Clients</div>
              </div>
              <div className="text-center space-y-3">
                <div className="text-4xl font-bold text-cyan-400 font-poppins">50+</div>
                <div className="text-blue-100">Countries Served</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-32 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16">
            <div className="space-y-8">
              <div>
                <Badge className="bg-blue-100 text-blue-700 border border-blue-200">
                  <Mail className="w-4 h-4 mr-2" />
                  Get In Touch
                </Badge>
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mt-4 font-poppins">
                  Ready to Transform Your
                  <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                    {" "}
                    Solar Operations?
                  </span>
                </h2>
                <p className="text-xl text-gray-600 mt-6">
                  Contact our team of experts to learn how our AI-powered forecasting can optimize your solar energy
                  generation.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Phone className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Phone</h4>
                    <p className="text-gray-600">+91 98765 43210</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Email</h4>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Location className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Address</h4>
                    <p className="text-gray-600">Mumbai, Maharashtra, India</p>
                  </div>
                </div>
              </div>
            </div>

            <Card className="shadow-xl">
              <div className="p-8 space-y-6">
                <h3 className="text-2xl font-bold text-gray-900 font-poppins">Send us a message</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <Input placeholder="Enter your first name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <Input placeholder="Enter your last name" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                  <Input placeholder="Enter your company name" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 h-32 resize-none"
                    placeholder="Tell us about your solar energy forecasting needs..."
                  ></textarea>
                </div>
                <Button className="w-full" size="lg">
                  <Send className="w-5 h-5 mr-2" />
                  Send Message
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 bg-gradient-to-r from-blue-600 to-cyan-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-6xl font-bold text-white font-poppins">
              Ready to Optimize Your
              <br />
              Solar Energy Production?
            </h2>
            <p className="text-xl text-blue-100">
              Join hundreds of solar energy producers who trust our AI-powered forecasting platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 shadow-xl">
                <Rocket className="w-5 h-5 mr-2" />
                Start Free Trial
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Calendar className="w-5 h-5 mr-2" />
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2 space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-cyan-500 rounded-xl flex items-center justify-center">
                  <Sun className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold font-poppins">Rajesh Power Service Limited</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Leading the future of renewable energy with AI-powered forecasting solutions. Transforming solar energy
                operations worldwide.
              </p>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Share2 className="w-5 h-5" />
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Download className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {[
              {
                title: "Solutions",
                links: ["Solar Forecasting", "Energy Analytics", "Performance Monitoring", "Consulting Services"],
              },
              {
                title: "Company",
                links: ["About Us", "Careers", "News", "Contact"],
              },
              {
                title: "Support",
                links: ["Documentation", "Help Center", "API Reference", "Status"],
              },
            ].map((section, index) => (
              <div key={index} className="space-y-4">
                <h3 className="text-lg font-semibold text-white font-poppins">{section.title}</h3>
                <ul className="space-y-2">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a href="#" className="text-gray-400 hover:text-white transition-colors">
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2024 Rajesh Power Service Limited. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      {isLoginOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <div className="p-8 space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-gray-900 font-poppins">Login</h2>
                <button onClick={() => setIsLoginOpen(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="w-6 h-6" />
                </button>
              </div>
              <p className="text-gray-600">Access your solar forecasting dashboard</p>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <Input type="email" placeholder="Enter your email" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <Input type="password" placeholder="Enter your password" />
                </div>
                <Button className="w-full">Sign In</Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}
